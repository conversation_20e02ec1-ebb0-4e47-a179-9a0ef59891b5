/**
 * 🔧 Create Admin User Script - Quick Setup for Development
 * Creates admin user directly in MongoDB for testing
 */

require('dotenv').config();
const mongoose = require('mongoose');
const { generate: uniqueId } = require('shortid');

// Import models
require('./src/models/coreModels/Admin');
require('./src/models/coreModels/AdminPassword');

const createAdmin = async () => {
  try {
    console.log('🚀 Connecting to MongoDB...');
    
    await mongoose.connect(process.env.DATABASE || 'mongodb://localhost:27017/idurar', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB');

    const Admin = mongoose.model('Admin');
    const AdminPassword = mongoose.model('AdminPassword');

    // Check if admin already exists
    const existingAdmin = await Admin.findOne({ email: '<EMAIL>' });
    if (existingAdmin) {
      console.log('ℹ️ Admin user already exists');
      process.exit(0);
    }

    console.log('👤 Creating admin user...');

    // Create admin user
    const adminData = {
      email: '<EMAIL>',
      name: 'HVAC Admin',
      role: 'owner',
      enabled: true,
      removed: false
    };

    const admin = new Admin(adminData);
    const savedAdmin = await admin.save();

    console.log('✅ Admin user created:', savedAdmin._id);

    // Create admin password
    const newAdminPassword = new AdminPassword();
    const salt = uniqueId();
    const passwordHash = newAdminPassword.generateHash(salt, 'admin123');

    const adminPasswordData = {
      password: passwordHash,
      emailVerified: true,
      salt: salt,
      user: savedAdmin._id,
      loggedSessions: [],
      removed: false
    };

    const adminPassword = new AdminPassword(adminPasswordData);
    await adminPassword.save();

    console.log('✅ Admin password created');
    console.log('');
    console.log('🎉 Admin user created successfully!');
    console.log('📧 Email: <EMAIL>');
    console.log('🔑 Password: admin123');
    console.log('');

    process.exit(0);

  } catch (error) {
    console.error('❌ Error creating admin user:', error);
    process.exit(1);
  }
};

createAdmin();
