{"timestamp":"2025-06-02T13:37:16.800Z","level":"error","message":"❌ Error fetching attachment:","context":{"stringValue":"\"health\"","valueType":"string","kind":"ObjectId","value":"health","path":"_id","reason":{},"name":"CastError","message":"Cast to ObjectId failed for value \"health\" (type string) at path \"_id\" for model \"Attachment\""},"pid":550193}
{"timestamp":"2025-06-02T13:58:19.726Z","level":"error","message":"❌ MinIO initialization failed:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T13:58:19.728Z","level":"error","message":"❌ Email processing failed:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T13:58:19.728Z","level":"error","message":"❌ Error starting email processing:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T13:59:39.227Z","level":"error","message":"❌ MinIO initialization failed:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T13:59:39.227Z","level":"error","message":"❌ Email processing failed:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T13:59:39.228Z","level":"error","message":"❌ Error starting email processing:","context":{"name":"S3Error"},"pid":560262}
{"timestamp":"2025-06-02T14:01:11.177Z","level":"error","message":"❌ MinIO initialization failed:","context":{"name":"S3Error"},"pid":562824}
{"timestamp":"2025-06-02T14:01:11.179Z","level":"error","message":"❌ Email processing failed:","context":{"name":"S3Error"},"pid":562824}
{"timestamp":"2025-06-02T14:01:11.179Z","level":"error","message":"❌ Error starting email processing:","context":{"name":"S3Error"},"pid":562824}
