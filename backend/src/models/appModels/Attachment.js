/**
 * 📎 Attachment Model - Enhanced Email Attachment Management
 * Professional attachment tracking with MinIO integration and AI analysis
 */

const mongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');

const attachmentSchema = new mongoose.Schema({
  // Basic file information
  filename: {
    type: String,
    required: true,
    trim: true
  },
  originalName: {
    type: String,
    required: true,
    trim: true
  },
  mimeType: {
    type: String,
    required: true
  },
  size: {
    type: Number,
    required: true
  },
  
  // MinIO storage information
  bucket: {
    type: String,
    required: true
  },
  objectName: {
    type: String,
    required: true,
    unique: true
  },
  url: {
    type: String,
    required: true
  },
  publicUrl: {
    type: String // For processed images/documents
  },
  
  // Classification
  category: {
    type: String,
    enum: [
      'invoice',
      'contract',
      'protocol',
      'transcription',
      'image',
      'document',
      'general'
    ],
    default: 'general'
  },
  type: {
    type: String,
    enum: [
      'audio',
      'documents',
      'images',
      'spreadsheets',
      'invoices',
      'other'
    ],
    required: true
  },
  
  // Customer relationship
  customerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Client',
    index: true
  },
  
  // Email context
  emailInfo: {
    subject: String,
    from: String,
    date: Date,
    messageId: String,
    account: {
      type: String,
      enum: ['dolores', 'grzegorz']
    }
  },
  
  // Processing status
  processingStatus: {
    type: String,
    enum: [
      'uploaded',
      'processing',
      'processed',
      'ready_for_transcription',
      'transcribed',
      'analyzed',
      'error'
    ],
    default: 'uploaded',
    index: true
  },
  
  // Error handling
  errorMessage: String,
  retryCount: {
    type: Number,
    default: 0
  },
  
  // AI Analysis results
  aiAnalysis: {
    type: {
      type: String,
      enum: ['audio', 'document', 'image', 'invoice']
    },
    extractedText: String,
    isInvoice: Boolean,
    invoiceData: {
      invoiceNumber: String,
      date: String,
      dueDate: String,
      amount: Number,
      currency: String,
      vendor: String,
      taxNumber: String,
      items: [{
        description: String,
        amount: Number
      }],
      confidence: Number
    },
    transcriptionData: {
      text: String,
      confidence: Number,
      language: String,
      duration: Number,
      speakers: Number
    },
    sentiment: {
      type: String,
      enum: ['positive', 'negative', 'neutral']
    },
    topics: [String],
    actionItems: [String],
    urgency: {
      type: String,
      enum: ['low', 'medium', 'high', 'critical']
    },
    category: String,
    estimatedCost: Number,
    recommendedAction: String,
    confidence: Number,
    processedAt: Date,
    readyForTranscription: Boolean,
    estimatedDuration: String,
    needsAnalysis: Boolean,
    needsManualReview: Boolean
  },
  
  // Metadata from MinIO
  metadata: {
    type: Map,
    of: String
  },
  
  // Relationships
  relatedServiceOrder: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'ServiceOrder'
  },
  relatedInvoice: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Invoice'
  },
  relatedOpportunity: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Opportunity'
  },
  
  // Tags for organization
  tags: [String],
  
  // Access control
  isPublic: {
    type: Boolean,
    default: false
  },
  accessLevel: {
    type: String,
    enum: ['private', 'internal', 'customer', 'public'],
    default: 'internal'
  },
  
  // Audit trail
  uploadedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  processedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Admin'
  },
  
  // Timestamps
  uploadedAt: {
    type: Date,
    default: Date.now
  },
  processedAt: Date,
  lastAccessedAt: Date,
  
  // Versioning
  version: {
    type: Number,
    default: 1
  },
  parentAttachment: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Attachment'
  },
  
  // Quality metrics
  qualityScore: {
    type: Number,
    min: 0,
    max: 100
  },
  
  // Business context
  businessImpact: {
    type: String,
    enum: ['low', 'medium', 'high', 'critical']
  },
  priority: {
    type: String,
    enum: ['low', 'normal', 'high', 'urgent'],
    default: 'normal'
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Add pagination plugin
attachmentSchema.plugin(mongoosePaginate);

// Indexes for performance
attachmentSchema.index({ customerId: 1, uploadedAt: -1 });
attachmentSchema.index({ processingStatus: 1, uploadedAt: -1 });
attachmentSchema.index({ 'emailInfo.account': 1, uploadedAt: -1 });
attachmentSchema.index({ category: 1, type: 1 });
attachmentSchema.index({ 'aiAnalysis.isInvoice': 1 });
attachmentSchema.index({ tags: 1 });

// Virtual for file extension
attachmentSchema.virtual('extension').get(function() {
  return this.filename.split('.').pop().toLowerCase();
});

// Virtual for human readable size
attachmentSchema.virtual('humanSize').get(function() {
  const bytes = this.size;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  if (bytes === 0) return '0 Bytes';
  const i = Math.floor(Math.log(bytes) / Math.log(1024));
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
});

// Virtual for processing duration
attachmentSchema.virtual('processingDuration').get(function() {
  if (this.processedAt && this.uploadedAt) {
    return Math.round((this.processedAt - this.uploadedAt) / 1000); // seconds
  }
  return null;
});

// Virtual for age
attachmentSchema.virtual('age').get(function() {
  const now = new Date();
  const uploaded = this.uploadedAt;
  const diffTime = Math.abs(now - uploaded);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays;
});

// Methods
attachmentSchema.methods.markAsProcessed = function() {
  this.processingStatus = 'processed';
  this.processedAt = new Date();
  return this.save();
};

attachmentSchema.methods.markAsError = function(errorMessage) {
  this.processingStatus = 'error';
  this.errorMessage = errorMessage;
  this.retryCount += 1;
  return this.save();
};

attachmentSchema.methods.updateLastAccessed = function() {
  this.lastAccessedAt = new Date();
  return this.save();
};

attachmentSchema.methods.addTag = function(tag) {
  if (!this.tags.includes(tag)) {
    this.tags.push(tag);
    return this.save();
  }
  return Promise.resolve(this);
};

attachmentSchema.methods.removeTag = function(tag) {
  this.tags = this.tags.filter(t => t !== tag);
  return this.save();
};

// Static methods
attachmentSchema.statics.findByCustomer = function(customerId, options = {}) {
  const query = { customerId };
  
  if (options.category) {
    query.category = options.category;
  }
  
  if (options.type) {
    query.type = options.type;
  }
  
  if (options.processingStatus) {
    query.processingStatus = options.processingStatus;
  }
  
  return this.find(query)
    .sort({ uploadedAt: -1 })
    .limit(options.limit || 50)
    .populate('customerId', 'name email', 'Client')
    .populate('relatedServiceOrder', 'orderNumber status')
    .populate('relatedInvoice', 'number amount')
    .populate('relatedOpportunity', 'title stage');
};

attachmentSchema.statics.findPendingProcessing = function() {
  return this.find({
    processingStatus: { $in: ['uploaded', 'processing', 'ready_for_transcription'] }
  }).sort({ uploadedAt: 1 });
};

attachmentSchema.statics.getStatsByCustomer = function(customerId) {
  return this.aggregate([
    { $match: { customerId: mongoose.Types.ObjectId(customerId) } },
    {
      $group: {
        _id: '$type',
        count: { $sum: 1 },
        totalSize: { $sum: '$size' },
        avgQuality: { $avg: '$qualityScore' }
      }
    }
  ]);
};

attachmentSchema.statics.getProcessingStats = function() {
  return this.aggregate([
    {
      $group: {
        _id: '$processingStatus',
        count: { $sum: 1 },
        totalSize: { $sum: '$size' }
      }
    }
  ]);
};

// Pre-save middleware
attachmentSchema.pre('save', function(next) {
  // Auto-generate tags based on content
  if (this.isNew) {
    const autoTags = [];
    
    if (this.category === 'invoice') {
      autoTags.push('financial');
    }
    
    if (this.type === 'audio') {
      autoTags.push('transcription');
    }
    
    if (this.emailInfo?.account) {
      autoTags.push(`account-${this.emailInfo.account}`);
    }
    
    this.tags = [...new Set([...this.tags, ...autoTags])];
  }
  
  next();
});

// Post-save middleware for notifications
attachmentSchema.post('save', function(doc) {
  // Emit events for real-time updates
  if (doc.processingStatus === 'processed') {
    // Notify that processing is complete
    process.emit('attachment:processed', doc);
  }
  
  if (doc.aiAnalysis?.isInvoice) {
    // Notify about new invoice
    process.emit('invoice:detected', doc);
  }
});

module.exports = mongoose.model('Attachment', attachmentSchema);
