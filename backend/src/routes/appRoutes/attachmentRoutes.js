/**
 * 📎 Attachment Routes - Enhanced Email Attachment Management API
 * Professional API endpoints for attachment processing and management
 */

const express = require('express');
const router = express.Router();
const multer = require('multer');
const Attachment = require('../../models/appModels/Attachment');
const enhancedEmailAttachmentProcessor = require('../../services/enhancedEmailAttachmentProcessor');
const minioService = require('../../services/minioService');
const ocrService = require('../../services/ocrService');
const { requireAuth } = require('../../middlewares/requireAuth');
const logger = require('../../utils/logger');

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB limit
  },
  fileFilter: (req, file, cb) => {
    // Allow all file types for now
    cb(null, true);
  }
});

/**
 * @route GET /api/attachments/health
 * @desc Health check for attachment services
 */
router.get('/health', async (req, res) => {
  try {
    const minioHealth = await minioService.healthCheck();
    const ocrHealth = await ocrService.healthCheck();
    const processorHealth = await enhancedEmailAttachmentProcessor.healthCheck();

    res.json({
      success: true,
      services: {
        minio: minioHealth,
        ocr: ocrHealth,
        processor: processorHealth
      },
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('❌ Health check failed:', error);
    res.status(500).json({
      success: false,
      message: 'Health check failed',
      error: error.message
    });
  }
});

/**
 * @route GET /api/attachments
 * @desc Get all attachments with filtering and pagination
 */
router.get('/', requireAuth, async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      customerId,
      category,
      type,
      processingStatus,
      search
    } = req.query;

    const query = {};
    
    if (customerId) query.customerId = customerId;
    if (category) query.category = category;
    if (type) query.type = type;
    if (processingStatus) query.processingStatus = processingStatus;
    
    if (search) {
      query.$or = [
        { filename: { $regex: search, $options: 'i' } },
        { originalName: { $regex: search, $options: 'i' } },
        { 'emailInfo.subject': { $regex: search, $options: 'i' } },
        { tags: { $in: [new RegExp(search, 'i')] } }
      ];
    }

    const options = {
      page: parseInt(page),
      limit: parseInt(limit),
      sort: { uploadedAt: -1 },
      populate: [
        { path: 'customerId', select: 'name email', model: 'Client' },
        { path: 'relatedServiceOrder', select: 'orderNumber status' },
        { path: 'relatedInvoice', select: 'number amount' }
      ]
    };

    const result = await Attachment.paginate(query, options);

    res.json({
      success: true,
      data: result.docs,
      pagination: {
        page: result.page,
        pages: result.totalPages,
        total: result.totalDocs,
        limit: result.limit
      }
    });
  } catch (error) {
    logger.error('❌ Error fetching attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachments',
      error: error.message
    });
  }
});

/**
 * @route GET /api/attachments/:id
 * @desc Get single attachment by ID
 */
router.get('/:id', requireAuth, async (req, res) => {
  try {
    const attachment = await Attachment.findById(req.params.id)
      .populate('customerId', 'name email', 'Client')
      .populate('relatedServiceOrder', 'orderNumber status')
      .populate('relatedInvoice', 'number amount');

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Update last accessed
    await attachment.updateLastAccessed();

    res.json({
      success: true,
      data: attachment
    });
  } catch (error) {
    logger.error('❌ Error fetching attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachment',
      error: error.message
    });
  }
});

/**
 * @route POST /api/attachments/upload
 * @desc Upload new attachment manually
 */
router.post('/upload', requireAuth, upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({
        success: false,
        message: 'No file provided'
      });
    }

    const { customerId, category = 'general', tags = [] } = req.body;

    // Generate object name
    const objectName = minioService.generateObjectName(
      req.file.originalname,
      customerId,
      category
    );

    // Upload to MinIO
    const uploadResult = await minioService.uploadBuffer(
      minioService.buckets.ATTACHMENTS_RAW,
      objectName,
      req.file.buffer,
      {
        'Original-Filename': req.file.originalname,
        'Content-Type': req.file.mimetype,
        'Uploaded-By': req.user.id,
        'Upload-Method': 'manual'
      }
    );

    // Create attachment record
    const attachment = new Attachment({
      filename: req.file.originalname,
      originalName: req.file.originalname,
      mimeType: req.file.mimetype,
      size: req.file.size,
      bucket: minioService.buckets.ATTACHMENTS_RAW,
      objectName: objectName,
      url: uploadResult.url,
      category,
      type: enhancedEmailAttachmentProcessor.getAttachmentType(req.file.originalname),
      customerId: customerId || null,
      tags: Array.isArray(tags) ? tags : [tags],
      uploadedBy: req.user.id,
      metadata: uploadResult.metadata
    });

    await attachment.save();

    res.json({
      success: true,
      message: 'File uploaded successfully',
      data: attachment
    });
  } catch (error) {
    logger.error('❌ Error uploading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to upload attachment',
      error: error.message
    });
  }
});

/**
 * @route GET /api/attachments/:id/download
 * @desc Download attachment file
 */
router.get('/:id/download', requireAuth, async (req, res) => {
  try {
    const attachment = await Attachment.findById(req.params.id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Get file buffer from MinIO
    const buffer = await minioService.getFileBuffer(
      attachment.bucket,
      attachment.objectName
    );

    // Update last accessed
    await attachment.updateLastAccessed();

    res.set({
      'Content-Type': attachment.mimeType,
      'Content-Disposition': `attachment; filename="${attachment.originalName}"`,
      'Content-Length': buffer.length
    });

    res.send(buffer);
  } catch (error) {
    logger.error('❌ Error downloading attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to download attachment',
      error: error.message
    });
  }
});

/**
 * @route POST /api/attachments/:id/process
 * @desc Manually trigger attachment processing
 */
router.post('/:id/process', requireAuth, async (req, res) => {
  try {
    const attachment = await Attachment.findById(req.params.id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    if (attachment.processingStatus === 'processed') {
      return res.status(400).json({
        success: false,
        message: 'Attachment already processed'
      });
    }

    // Update status to processing
    attachment.processingStatus = 'processing';
    attachment.processedBy = req.user.id;
    await attachment.save();

    // Get file buffer
    const buffer = await minioService.getFileBuffer(
      attachment.bucket,
      attachment.objectName
    );

    // Process based on type
    if (attachment.type === 'documents' || attachment.type === 'invoices') {
      const extractedText = await ocrService.extractText(buffer, attachment.mimeType);
      
      attachment.aiAnalysis = {
        type: 'document',
        extractedText: extractedText.substring(0, 2000),
        processedAt: new Date()
      };

      // Check if it's an invoice
      if (attachment.mimeType === 'application/pdf' || attachment.mimeType.startsWith('image/')) {
        const invoiceData = await ocrService.extractInvoiceData(extractedText);
        if (invoiceData.confidence > 60) {
          attachment.aiAnalysis.isInvoice = true;
          attachment.aiAnalysis.invoiceData = invoiceData;
          attachment.category = 'invoice';
        }
      }
    }

    await attachment.markAsProcessed();

    res.json({
      success: true,
      message: 'Attachment processed successfully',
      data: attachment
    });
  } catch (error) {
    logger.error('❌ Error processing attachment:', error);
    
    // Mark as error
    const attachment = await Attachment.findById(req.params.id);
    if (attachment) {
      await attachment.markAsError(error.message);
    }

    res.status(500).json({
      success: false,
      message: 'Failed to process attachment',
      error: error.message
    });
  }
});

/**
 * @route PUT /api/attachments/:id
 * @desc Update attachment metadata
 */
router.put('/:id', requireAuth, async (req, res) => {
  try {
    const { category, tags, customerId, priority, businessImpact } = req.body;

    const attachment = await Attachment.findById(req.params.id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Update fields
    if (category) attachment.category = category;
    if (tags) attachment.tags = Array.isArray(tags) ? tags : [tags];
    if (customerId) attachment.customerId = customerId;
    if (priority) attachment.priority = priority;
    if (businessImpact) attachment.businessImpact = businessImpact;

    await attachment.save();

    res.json({
      success: true,
      message: 'Attachment updated successfully',
      data: attachment
    });
  } catch (error) {
    logger.error('❌ Error updating attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to update attachment',
      error: error.message
    });
  }
});

/**
 * @route DELETE /api/attachments/:id
 * @desc Delete attachment
 */
router.delete('/:id', requireAuth, async (req, res) => {
  try {
    const attachment = await Attachment.findById(req.params.id);

    if (!attachment) {
      return res.status(404).json({
        success: false,
        message: 'Attachment not found'
      });
    }

    // Delete from MinIO
    await minioService.deleteFile(attachment.bucket, attachment.objectName);

    // Delete from database
    await attachment.deleteOne();

    res.json({
      success: true,
      message: 'Attachment deleted successfully'
    });
  } catch (error) {
    logger.error('❌ Error deleting attachment:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to delete attachment',
      error: error.message
    });
  }
});

/**
 * @route GET /api/attachments/customer/:customerId
 * @desc Get attachments for specific customer
 */
router.get('/customer/:customerId', requireAuth, async (req, res) => {
  try {
    const { customerId } = req.params;
    const { category, type, limit = 50 } = req.query;

    const attachments = await Attachment.findByCustomer(customerId, {
      category,
      type,
      limit: parseInt(limit)
    });

    res.json({
      success: true,
      data: attachments
    });
  } catch (error) {
    logger.error('❌ Error fetching customer attachments:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch customer attachments',
      error: error.message
    });
  }
});

/**
 * @route GET /api/attachments/stats/overview
 * @desc Get attachment statistics overview
 */
router.get('/stats/overview', requireAuth, async (req, res) => {
  try {
    const stats = await Attachment.getProcessingStats();
    const totalCount = await Attachment.countDocuments();
    const totalSize = await Attachment.aggregate([
      { $group: { _id: null, totalSize: { $sum: '$size' } } }
    ]);

    res.json({
      success: true,
      data: {
        totalCount,
        totalSize: totalSize[0]?.totalSize || 0,
        byStatus: stats,
        timestamp: new Date().toISOString()
      }
    });
  } catch (error) {
    logger.error('❌ Error fetching attachment stats:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to fetch attachment statistics',
      error: error.message
    });
  }
});

/**
 * @route POST /api/attachments/process/email
 * @desc Trigger email attachment processing
 */
router.post('/process/email', requireAuth, async (req, res) => {
  try {
    await enhancedEmailAttachmentProcessor.startProcessing();

    res.json({
      success: true,
      message: 'Email attachment processing started'
    });
  } catch (error) {
    logger.error('❌ Error starting email processing:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to start email processing',
      error: error.message
    });
  }
});

module.exports = router;
