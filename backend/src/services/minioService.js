/**
 * 🗄️ MinIO Service - Enhanced Object Storage for HVAC CRM
 * Professional bucket management for email attachments, invoices, and documents
 */

const Minio = require('minio');
const path = require('path');
const fs = require('fs').promises;
const logger = require('../utils/logger');

class MinioService {
  constructor() {
    this.client = new Minio.Client({
      endPoint: process.env.MINIO_ENDPOINT || 'localhost',
      port: parseInt(process.env.MINIO_PORT) || 9001,
      useSSL: process.env.MINIO_USE_SSL === 'true',
      accessKey: process.env.MINIO_ROOT_USER || process.env.MINIO_ACCESS_KEY || 'minioadmin',
      secretKey: process.env.MINIO_ROOT_PASSWORD || process.env.MINIO_SECRET_KEY || 'minioadmin123',
    });

    this.buckets = {
      ATTACHMENTS_RAW: 'hvac-attachments-raw',
      ATTACHMENTS_PROCESSED: 'hvac-attachments-processed',
      INVOICES: 'hvac-invoices',
      DOCUMENTS: 'hvac-documents',
      TRANSCRIPTIONS: 'hvac-transcriptions',
      IMAGES: 'hvac-images'
    };

    this.initialized = false;
  }

  /**
   * Initialize MinIO service and create buckets
   */
  async initialize() {
    try {
      logger.info('🚀 Initializing MinIO service...');

      // Test connection
      await this.client.listBuckets();
      logger.info('✅ MinIO connection successful');

      // Create buckets if they don't exist
      for (const [name, bucket] of Object.entries(this.buckets)) {
        const exists = await this.client.bucketExists(bucket);
        if (!exists) {
          await this.client.makeBucket(bucket, 'us-east-1');
          logger.info(`✅ Created bucket: ${bucket}`);

          // Set bucket policy for public read access to processed files
          if (bucket.includes('processed') || bucket.includes('images')) {
            await this.setBucketPolicy(bucket, 'public-read');
          }
        } else {
          logger.info(`ℹ️ Bucket already exists: ${bucket}`);
        }
      }

      this.initialized = true;
      logger.info('🎉 MinIO service initialized successfully');
    } catch (error) {
      logger.error('❌ MinIO initialization failed:', error);
      throw error;
    }
  }

  /**
   * Set bucket policy
   */
  async setBucketPolicy(bucketName, policy = 'private') {
    try {
      const policyConfig = {
        Version: '2012-10-17',
        Statement: [
          {
            Effect: 'Allow',
            Principal: { AWS: ['*'] },
            Action: ['s3:GetObject'],
            Resource: [`arn:aws:s3:::${bucketName}/*`]
          }
        ]
      };

      if (policy === 'public-read') {
        await this.client.setBucketPolicy(bucketName, JSON.stringify(policyConfig));
        logger.info(`✅ Set public-read policy for bucket: ${bucketName}`);
      }
    } catch (error) {
      logger.warn(`⚠️ Could not set policy for bucket ${bucketName}:`, error.message);
    }
  }

  /**
   * Upload file to MinIO
   */
  async uploadFile(bucketName, objectName, filePath, metadata = {}) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const stats = await fs.stat(filePath);
      const metaData = {
        'Content-Type': this.getContentType(objectName),
        'Upload-Date': new Date().toISOString(),
        'File-Size': stats.size.toString(),
        ...metadata
      };

      const result = await this.client.fPutObject(bucketName, objectName, filePath, metaData);
      
      logger.info(`✅ File uploaded: ${objectName} to ${bucketName}`);
      
      return {
        success: true,
        bucket: bucketName,
        objectName,
        etag: result.etag,
        size: stats.size,
        url: await this.getFileUrl(bucketName, objectName),
        metadata: metaData
      };
    } catch (error) {
      logger.error(`❌ Upload failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Upload buffer to MinIO
   */
  async uploadBuffer(bucketName, objectName, buffer, metadata = {}) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const metaData = {
        'Content-Type': this.getContentType(objectName),
        'Upload-Date': new Date().toISOString(),
        'File-Size': buffer.length.toString(),
        ...metadata
      };

      const result = await this.client.putObject(bucketName, objectName, buffer, metaData);
      
      logger.info(`✅ Buffer uploaded: ${objectName} to ${bucketName}`);
      
      return {
        success: true,
        bucket: bucketName,
        objectName,
        etag: result.etag,
        size: buffer.length,
        url: await this.getFileUrl(bucketName, objectName),
        metadata: metaData
      };
    } catch (error) {
      logger.error(`❌ Buffer upload failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Download file from MinIO
   */
  async downloadFile(bucketName, objectName, downloadPath) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      await this.client.fGetObject(bucketName, objectName, downloadPath);
      logger.info(`✅ File downloaded: ${objectName} from ${bucketName}`);
      
      return {
        success: true,
        localPath: downloadPath
      };
    } catch (error) {
      logger.error(`❌ Download failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Get file as buffer
   */
  async getFileBuffer(bucketName, objectName) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const stream = await this.client.getObject(bucketName, objectName);
      const chunks = [];
      
      return new Promise((resolve, reject) => {
        stream.on('data', chunk => chunks.push(chunk));
        stream.on('end', () => resolve(Buffer.concat(chunks)));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error(`❌ Get buffer failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Get file URL
   */
  async getFileUrl(bucketName, objectName, expiry = 24 * 60 * 60) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      return await this.client.presignedGetObject(bucketName, objectName, expiry);
    } catch (error) {
      logger.error(`❌ Get URL failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Delete file from MinIO
   */
  async deleteFile(bucketName, objectName) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      await this.client.removeObject(bucketName, objectName);
      logger.info(`✅ File deleted: ${objectName} from ${bucketName}`);
      
      return { success: true };
    } catch (error) {
      logger.error(`❌ Delete failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * List files in bucket
   */
  async listFiles(bucketName, prefix = '', recursive = true) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const objects = [];
      const stream = this.client.listObjects(bucketName, prefix, recursive);
      
      return new Promise((resolve, reject) => {
        stream.on('data', obj => objects.push(obj));
        stream.on('end', () => resolve(objects));
        stream.on('error', reject);
      });
    } catch (error) {
      logger.error(`❌ List files failed for bucket ${bucketName}:`, error);
      throw error;
    }
  }

  /**
   * Get file metadata
   */
  async getFileMetadata(bucketName, objectName) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      const stat = await this.client.statObject(bucketName, objectName);
      return {
        size: stat.size,
        lastModified: stat.lastModified,
        etag: stat.etag,
        contentType: stat.metaData['content-type'],
        metadata: stat.metaData
      };
    } catch (error) {
      logger.error(`❌ Get metadata failed for ${objectName}:`, error);
      throw error;
    }
  }

  /**
   * Get content type based on file extension
   */
  getContentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    const contentTypes = {
      '.pdf': 'application/pdf',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.png': 'image/png',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.mp3': 'audio/mpeg',
      '.m4a': 'audio/mp4',
      '.wav': 'audio/wav',
      '.txt': 'text/plain',
      '.json': 'application/json',
      '.xml': 'application/xml',
      '.doc': 'application/msword',
      '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      '.xls': 'application/vnd.ms-excel',
      '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    };
    
    return contentTypes[ext] || 'application/octet-stream';
  }

  /**
   * Generate unique object name
   */
  generateObjectName(originalName, customerId = null, category = 'general') {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const extension = path.extname(originalName);
    const baseName = path.basename(originalName, extension);
    
    const prefix = customerId ? `${customerId}/${category}` : category;
    return `${prefix}/${timestamp}-${baseName}${extension}`;
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      await this.client.listBuckets();
      return { status: 'healthy', timestamp: new Date().toISOString() };
    } catch (error) {
      return { status: 'unhealthy', error: error.message, timestamp: new Date().toISOString() };
    }
  }
}

// Export singleton instance
module.exports = new MinioService();
