/**
 * 📧 Enhanced Email Attachment Processor - Corporate Level
 * Professional email attachment processing with MinIO storage and AI analysis
 */

const Imap = require('imap');
const { simpleParser } = require('mailparser');
const fs = require('fs').promises;
const path = require('path');
const minioService = require('./minioService');
const ocrService = require('./ocrService');
const logger = require('../utils/logger');
const Client = require('../models/appModels/Client');
const Attachment = require('../models/appModels/Attachment');

class EnhancedEmailAttachmentProcessor {
  constructor() {
    this.emailAccounts = [
      {
        name: 'dolores',
        config: {
          user: '<EMAIL>',
          password: process.env.DOLORES_EMAIL_PASSWORD || 'Blaeritipol1',
          host: 'imap.gmail.com',
          port: 993,
          tls: true,
          tlsOptions: { rejectUnauthorized: false }
        },
        category: 'transcriptions'
      },
      {
        name: 'grz<PERSON><PERSON>',
        config: {
          user: 'grz<PERSON><PERSON>@koldbringers.pl',
          password: process.env.GRZEGORZ_EMAIL_PASSWORD || 'Blaeritipol1',
          host: 'imap.gmail.com',
          port: 993,
          tls: true,
          tlsOptions: { rejectUnauthorized: false }
        },
        category: 'customer_emails'
      }
    ];

    this.supportedAttachmentTypes = {
      audio: ['.m4a', '.mp3', '.wav', '.aac'],
      documents: ['.pdf', '.doc', '.docx', '.txt'],
      images: ['.jpg', '.jpeg', '.png', '.gif', '.webp'],
      spreadsheets: ['.xls', '.xlsx', '.csv'],
      invoices: ['.pdf', '.jpg', '.jpeg', '.png']
    };

    this.processing = false;
  }

  /**
   * Start email processing for all accounts
   */
  async startProcessing() {
    if (this.processing) {
      logger.warn('⚠️ Email processing already running');
      return;
    }

    this.processing = true;
    logger.info('🚀 Starting enhanced email attachment processing...');

    try {
      // Initialize MinIO
      await minioService.initialize();

      // Process each email account
      for (const account of this.emailAccounts) {
        await this.processEmailAccount(account);
      }

      logger.info('✅ Email processing completed successfully');
    } catch (error) {
      logger.error('❌ Email processing failed:', error);
      throw error;
    } finally {
      this.processing = false;
    }
  }

  /**
   * Process single email account
   */
  async processEmailAccount(account) {
    return new Promise((resolve, reject) => {
      logger.info(`📧 Processing emails for: ${account.name}`);

      const imap = new Imap(account.config);

      imap.once('ready', async () => {
        try {
          imap.openBox('INBOX', false, async (err, box) => {
            if (err) {
              reject(err);
              return;
            }

            // Search for unread emails with attachments
            imap.search(['UNSEEN', 'HEADER', 'X-GM-RAW', 'has:attachment'], async (err, results) => {
              if (err) {
                reject(err);
                return;
              }

              if (results.length === 0) {
                logger.info(`ℹ️ No new emails with attachments for ${account.name}`);
                imap.end();
                resolve();
                return;
              }

              logger.info(`📬 Found ${results.length} emails with attachments for ${account.name}`);

              // Process each email
              const fetch = imap.fetch(results, { bodies: '', struct: true });
              let processedCount = 0;

              fetch.on('message', (msg, seqno) => {
                this.processEmailMessage(msg, seqno, account)
                  .then(() => {
                    processedCount++;
                    if (processedCount === results.length) {
                      imap.end();
                      resolve();
                    }
                  })
                  .catch(reject);
              });

              fetch.once('error', reject);
            });
          });
        } catch (error) {
          reject(error);
        }
      });

      imap.once('error', reject);
      imap.once('end', () => {
        logger.info(`✅ Finished processing ${account.name}`);
      });

      imap.connect();
    });
  }

  /**
   * Process individual email message
   */
  async processEmailMessage(msg, seqno, account) {
    return new Promise((resolve, reject) => {
      msg.on('body', async (stream, info) => {
        try {
          const parsed = await simpleParser(stream);
          
          logger.info(`📧 Processing email: ${parsed.subject} from ${parsed.from?.text}`);

          // Extract customer information
          const customerInfo = await this.extractCustomerInfo(parsed, account);

          // Process attachments
          if (parsed.attachments && parsed.attachments.length > 0) {
            for (const attachment of parsed.attachments) {
              await this.processAttachment(attachment, parsed, customerInfo, account);
            }
          }

          // Mark as read
          msg.once('attributes', (attrs) => {
            const uid = attrs.uid;
            // Mark as read (remove \Seen flag handling for now)
          });

          resolve();
        } catch (error) {
          logger.error(`❌ Error processing email ${seqno}:`, error);
          reject(error);
        }
      });

      msg.once('error', reject);
    });
  }

  /**
   * Extract customer information from email
   */
  async extractCustomerInfo(email, account) {
    try {
      const fromEmail = email.from?.value?.[0]?.address || email.from?.text;
      const fromName = email.from?.value?.[0]?.name || email.from?.text;

      // Try to find existing customer
      let customer = null;
      if (fromEmail) {
        customer = await Client.findOne({
          $or: [
            { email: fromEmail },
            { 'contactInfo.email': fromEmail }
          ]
        });
      }

      // Extract customer info from email content if not found
      if (!customer) {
        const extractedInfo = await this.extractCustomerFromContent(email);
        customer = extractedInfo;
      }

      return {
        customer,
        fromEmail,
        fromName,
        subject: email.subject,
        date: email.date,
        messageId: email.messageId,
        account: account.name
      };
    } catch (error) {
      logger.error('❌ Error extracting customer info:', error);
      return {
        customer: null,
        fromEmail: null,
        fromName: null,
        subject: email.subject,
        date: email.date,
        messageId: email.messageId,
        account: account.name
      };
    }
  }

  /**
   * Extract customer information from email content using AI
   */
  async extractCustomerFromContent(email) {
    try {
      // This would use AI service to extract customer info
      // For now, return basic structure
      return {
        extractedFromEmail: true,
        subject: email.subject,
        content: email.text || email.html,
        needsManualReview: true
      };
    } catch (error) {
      logger.error('❌ Error extracting customer from content:', error);
      return null;
    }
  }

  /**
   * Process individual attachment
   */
  async processAttachment(attachment, email, customerInfo, account) {
    try {
      logger.info(`📎 Processing attachment: ${attachment.filename}`);

      // Determine attachment type and category
      const attachmentType = this.getAttachmentType(attachment.filename);
      const category = this.getAttachmentCategory(attachment.filename, email.subject);

      // Generate unique object name
      const customerId = customerInfo.customer?._id || 'unknown';
      const objectName = minioService.generateObjectName(
        attachment.filename,
        customerId,
        `${account.category}/${category}`
      );

      // Upload to MinIO raw bucket
      const uploadResult = await minioService.uploadBuffer(
        minioService.buckets.ATTACHMENTS_RAW,
        objectName,
        attachment.content,
        {
          'Original-Filename': attachment.filename,
          'Content-Type': attachment.contentType,
          'Email-Subject': email.subject,
          'Email-From': customerInfo.fromEmail || 'unknown',
          'Email-Date': email.date?.toISOString() || new Date().toISOString(),
          'Account': account.name,
          'Category': category,
          'Customer-ID': customerId
        }
      );

      // Create attachment record in database
      const attachmentRecord = new Attachment({
        filename: attachment.filename,
        originalName: attachment.filename,
        mimeType: attachment.contentType,
        size: attachment.size,
        bucket: minioService.buckets.ATTACHMENTS_RAW,
        objectName: objectName,
        url: uploadResult.url,
        category: category,
        type: attachmentType,
        customerId: customerInfo.customer?._id,
        emailInfo: {
          subject: email.subject,
          from: customerInfo.fromEmail,
          date: email.date,
          messageId: email.messageId,
          account: account.name
        },
        processingStatus: 'uploaded',
        metadata: uploadResult.metadata
      });

      await attachmentRecord.save();

      // Process based on type
      await this.processAttachmentByType(attachmentRecord, attachment);

      logger.info(`✅ Attachment processed: ${attachment.filename}`);

      return attachmentRecord;
    } catch (error) {
      logger.error(`❌ Error processing attachment ${attachment.filename}:`, error);
      throw error;
    }
  }

  /**
   * Process attachment based on its type
   */
  async processAttachmentByType(attachmentRecord, attachment) {
    try {
      switch (attachmentRecord.type) {
        case 'audio':
          await this.processAudioAttachment(attachmentRecord);
          break;
        case 'documents':
        case 'invoices':
          await this.processDocumentAttachment(attachmentRecord);
          break;
        case 'images':
          await this.processImageAttachment(attachmentRecord);
          break;
        default:
          logger.info(`ℹ️ No specific processing for type: ${attachmentRecord.type}`);
      }
    } catch (error) {
      logger.error(`❌ Error processing ${attachmentRecord.type} attachment:`, error);
      attachmentRecord.processingStatus = 'error';
      attachmentRecord.errorMessage = error.message;
      await attachmentRecord.save();
    }
  }

  /**
   * Process audio attachment (M4A transcription)
   */
  async processAudioAttachment(attachmentRecord) {
    try {
      logger.info(`🎵 Processing audio attachment: ${attachmentRecord.filename}`);
      
      // This would integrate with transcription service
      // For now, mark as ready for transcription
      attachmentRecord.processingStatus = 'ready_for_transcription';
      attachmentRecord.aiAnalysis = {
        type: 'audio',
        readyForTranscription: true,
        estimatedDuration: 'unknown'
      };

      await attachmentRecord.save();
      logger.info(`✅ Audio attachment ready for transcription: ${attachmentRecord.filename}`);
    } catch (error) {
      logger.error(`❌ Error processing audio attachment:`, error);
      throw error;
    }
  }

  /**
   * Process document attachment (OCR + AI analysis)
   */
  async processDocumentAttachment(attachmentRecord) {
    try {
      logger.info(`📄 Processing document attachment: ${attachmentRecord.filename}`);
      
      // Get file buffer from MinIO
      const buffer = await minioService.getFileBuffer(
        attachmentRecord.bucket,
        attachmentRecord.objectName
      );

      // Perform OCR if needed
      let extractedText = '';
      if (attachmentRecord.mimeType.includes('pdf') || attachmentRecord.mimeType.includes('image')) {
        extractedText = await ocrService.extractText(buffer, attachmentRecord.mimeType);
      }

      // AI analysis for invoice detection
      const isInvoice = await this.detectInvoice(extractedText, attachmentRecord.filename);
      
      if (isInvoice) {
        await this.processInvoiceDocument(attachmentRecord, extractedText);
      }

      attachmentRecord.processingStatus = 'processed';
      attachmentRecord.aiAnalysis = {
        type: 'document',
        extractedText: extractedText.substring(0, 1000), // Store first 1000 chars
        isInvoice,
        processedAt: new Date()
      };

      await attachmentRecord.save();
      logger.info(`✅ Document attachment processed: ${attachmentRecord.filename}`);
    } catch (error) {
      logger.error(`❌ Error processing document attachment:`, error);
      throw error;
    }
  }

  /**
   * Process image attachment
   */
  async processImageAttachment(attachmentRecord) {
    try {
      logger.info(`🖼️ Processing image attachment: ${attachmentRecord.filename}`);
      
      // Move to images bucket for public access
      const buffer = await minioService.getFileBuffer(
        attachmentRecord.bucket,
        attachmentRecord.objectName
      );

      const imageObjectName = minioService.generateObjectName(
        attachmentRecord.filename,
        attachmentRecord.customerId,
        'images'
      );

      await minioService.uploadBuffer(
        minioService.buckets.IMAGES,
        imageObjectName,
        buffer,
        attachmentRecord.metadata
      );

      attachmentRecord.processingStatus = 'processed';
      attachmentRecord.publicUrl = await minioService.getFileUrl(
        minioService.buckets.IMAGES,
        imageObjectName
      );

      await attachmentRecord.save();
      logger.info(`✅ Image attachment processed: ${attachmentRecord.filename}`);
    } catch (error) {
      logger.error(`❌ Error processing image attachment:`, error);
      throw error;
    }
  }

  /**
   * Detect if document is an invoice
   */
  async detectInvoice(text, filename) {
    const invoiceKeywords = [
      'faktura', 'invoice', 'rachunek', 'bill',
      'kwota', 'amount', 'suma', 'total',
      'nip', 'tax', 'vat', 'podatek',
      'płatność', 'payment', 'termin'
    ];

    const lowerText = text.toLowerCase();
    const lowerFilename = filename.toLowerCase();

    const textMatches = invoiceKeywords.filter(keyword => 
      lowerText.includes(keyword)
    ).length;

    const filenameMatches = invoiceKeywords.filter(keyword => 
      lowerFilename.includes(keyword)
    ).length;

    return textMatches >= 3 || filenameMatches >= 1;
  }

  /**
   * Process invoice document
   */
  async processInvoiceDocument(attachmentRecord, extractedText) {
    try {
      logger.info(`💰 Processing invoice: ${attachmentRecord.filename}`);
      
      // This would integrate with invoice analysis service
      attachmentRecord.category = 'invoice';
      attachmentRecord.aiAnalysis = {
        ...attachmentRecord.aiAnalysis,
        isInvoice: true,
        invoiceData: {
          extractedText: extractedText.substring(0, 2000),
          needsAnalysis: true
        }
      };

      await attachmentRecord.save();
    } catch (error) {
      logger.error(`❌ Error processing invoice:`, error);
      throw error;
    }
  }

  /**
   * Get attachment type based on extension
   */
  getAttachmentType(filename) {
    const ext = path.extname(filename).toLowerCase();
    
    for (const [type, extensions] of Object.entries(this.supportedAttachmentTypes)) {
      if (extensions.includes(ext)) {
        return type;
      }
    }
    
    return 'other';
  }

  /**
   * Get attachment category based on filename and email subject
   */
  getAttachmentCategory(filename, subject = '') {
    const lowerFilename = filename.toLowerCase();
    const lowerSubject = subject.toLowerCase();
    
    if (lowerFilename.includes('faktura') || lowerSubject.includes('faktura')) {
      return 'invoice';
    }
    
    if (lowerFilename.includes('umowa') || lowerSubject.includes('umowa')) {
      return 'contract';
    }
    
    if (lowerFilename.includes('protokol') || lowerSubject.includes('protokol')) {
      return 'protocol';
    }
    
    if (path.extname(filename).toLowerCase() === '.m4a') {
      return 'transcription';
    }
    
    return 'general';
  }

  /**
   * Get processing statistics
   */
  async getProcessingStats() {
    try {
      const stats = await Attachment.aggregate([
        {
          $group: {
            _id: '$processingStatus',
            count: { $sum: 1 },
            totalSize: { $sum: '$size' }
          }
        }
      ]);

      const typeStats = await Attachment.aggregate([
        {
          $group: {
            _id: '$type',
            count: { $sum: 1 }
          }
        }
      ]);

      return {
        byStatus: stats,
        byType: typeStats,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      logger.error('❌ Error getting processing stats:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      const minioHealth = await minioService.healthCheck();
      
      return {
        status: this.processing ? 'processing' : 'ready',
        minio: minioHealth,
        accounts: this.emailAccounts.length,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'error',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }
}

module.exports = new EnhancedEmailAttachmentProcessor();
