/**
 * 🔍 OCR Service - Enhanced Text Extraction for HVAC CRM
 * Professional OCR processing for invoices, documents, and images
 */

const Tesseract = require('tesseract.js');
const pdf = require('pdf-parse');
const logger = require('../utils/logger');

class OCRService {
  constructor() {
    this.tesseractWorker = null;
    this.initialized = false;
  }

  /**
   * Initialize OCR service
   */
  async initialize() {
    try {
      if (this.initialized) {
        return;
      }

      logger.info('🚀 Initializing OCR service...');
      
      // Initialize Tesseract worker with Polish language support
      this.tesseractWorker = await Tesseract.createWorker('pol+eng', 1, {
        logger: m => {
          if (m.status === 'recognizing text') {
            logger.info(`📖 OCR Progress: ${Math.round(m.progress * 100)}%`);
          }
        }
      });

      await this.tesseractWorker.setParameters({
        tessedit_pageseg_mode: Tesseract.PSM.AUTO,
        tessedit_char_whitelist: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyzĄĆĘŁŃÓŚŹŻąćęłńóśźż0123456789.,;:!?()[]{}+-*/=@#$%^&|\\~`"\'<> \n\t',
      });

      this.initialized = true;
      logger.info('✅ OCR service initialized successfully');
    } catch (error) {
      logger.error('❌ OCR initialization failed:', error);
      throw error;
    }
  }

  /**
   * Extract text from buffer based on MIME type
   */
  async extractText(buffer, mimeType) {
    try {
      if (!this.initialized) {
        await this.initialize();
      }

      logger.info(`🔍 Extracting text from ${mimeType}...`);

      if (mimeType === 'application/pdf') {
        return await this.extractFromPDF(buffer);
      } else if (mimeType.startsWith('image/')) {
        return await this.extractFromImage(buffer);
      } else {
        throw new Error(`Unsupported MIME type: ${mimeType}`);
      }
    } catch (error) {
      logger.error(`❌ Text extraction failed for ${mimeType}:`, error);
      throw error;
    }
  }

  /**
   * Extract text from PDF
   */
  async extractFromPDF(buffer) {
    try {
      logger.info('📄 Extracting text from PDF...');
      
      const data = await pdf(buffer);
      let extractedText = data.text;

      // If PDF text extraction yields poor results, try OCR on PDF pages
      if (extractedText.length < 100 || this.isGarbledText(extractedText)) {
        logger.info('📄 PDF text extraction poor, attempting OCR...');
        // For now, return the extracted text even if poor
        // In production, you might want to convert PDF to images and OCR them
      }

      logger.info(`✅ Extracted ${extractedText.length} characters from PDF`);
      return this.cleanText(extractedText);
    } catch (error) {
      logger.error('❌ PDF text extraction failed:', error);
      throw error;
    }
  }

  /**
   * Extract text from image using OCR
   */
  async extractFromImage(buffer) {
    try {
      logger.info('🖼️ Extracting text from image using OCR...');
      
      const { data: { text, confidence } } = await this.tesseractWorker.recognize(buffer);
      
      logger.info(`✅ OCR completed with ${confidence}% confidence, extracted ${text.length} characters`);
      
      if (confidence < 60) {
        logger.warn(`⚠️ Low OCR confidence: ${confidence}%`);
      }

      return this.cleanText(text);
    } catch (error) {
      logger.error('❌ Image OCR failed:', error);
      throw error;
    }
  }

  /**
   * Extract structured data from invoice text
   */
  async extractInvoiceData(text) {
    try {
      logger.info('💰 Extracting structured invoice data...');

      const invoiceData = {
        invoiceNumber: this.extractInvoiceNumber(text),
        date: this.extractDate(text),
        dueDate: this.extractDueDate(text),
        amount: this.extractAmount(text),
        currency: this.extractCurrency(text),
        vendor: this.extractVendor(text),
        taxNumber: this.extractTaxNumber(text),
        items: this.extractLineItems(text),
        confidence: this.calculateExtractionConfidence(text)
      };

      logger.info(`✅ Invoice data extracted with ${invoiceData.confidence}% confidence`);
      return invoiceData;
    } catch (error) {
      logger.error('❌ Invoice data extraction failed:', error);
      throw error;
    }
  }

  /**
   * Extract invoice number
   */
  extractInvoiceNumber(text) {
    const patterns = [
      /(?:faktura|invoice|nr|number|numer)[\s:]*([A-Z0-9\/\-]+)/gi,
      /(?:FV|INV)[\s]*([0-9\/\-]+)/gi,
      /([0-9]{1,4}\/[0-9]{2,4})/g
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[0].replace(/^[^A-Z0-9]+/i, '').trim();
      }
    }

    return null;
  }

  /**
   * Extract date
   */
  extractDate(text) {
    const patterns = [
      /(?:data|date|dnia)[\s:]*([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/gi,
      /([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/g
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return this.normalizeDate(match[1] || match[0]);
      }
    }

    return null;
  }

  /**
   * Extract due date
   */
  extractDueDate(text) {
    const patterns = [
      /(?:termin|due|płatność|payment)[\s:]*([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/gi,
      /(?:do|until)[\s:]*([0-9]{1,2}[.\-\/][0-9]{1,2}[.\-\/][0-9]{2,4})/gi
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return this.normalizeDate(match[1]);
      }
    }

    return null;
  }

  /**
   * Extract amount
   */
  extractAmount(text) {
    const patterns = [
      /(?:suma|total|kwota|amount)[\s:]*([0-9\s]+[,.]?[0-9]*)\s*(?:zł|PLN|EUR|USD)/gi,
      /([0-9\s]+[,.]?[0-9]*)\s*(?:zł|PLN)/gi,
      /(?:do zapłaty|to pay)[\s:]*([0-9\s]+[,.]?[0-9]*)/gi
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        const amount = match[1].replace(/\s/g, '').replace(',', '.');
        return parseFloat(amount);
      }
    }

    return null;
  }

  /**
   * Extract currency
   */
  extractCurrency(text) {
    const currencies = ['PLN', 'EUR', 'USD', 'zł'];
    
    for (const currency of currencies) {
      if (text.includes(currency)) {
        return currency === 'zł' ? 'PLN' : currency;
      }
    }

    return 'PLN'; // Default to PLN for Polish invoices
  }

  /**
   * Extract vendor information
   */
  extractVendor(text) {
    // This is a simplified extraction - in production you'd use more sophisticated NLP
    const lines = text.split('\n').slice(0, 10); // Check first 10 lines
    
    for (const line of lines) {
      if (line.length > 10 && line.length < 100 && 
          !line.match(/faktura|invoice|data|numer/i)) {
        return line.trim();
      }
    }

    return null;
  }

  /**
   * Extract tax number (NIP)
   */
  extractTaxNumber(text) {
    const patterns = [
      /(?:NIP|Tax ID)[\s:]*([0-9\-\s]{10,15})/gi,
      /([0-9]{3}[\-\s]?[0-9]{3}[\-\s]?[0-9]{2}[\-\s]?[0-9]{2})/g
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1].replace(/[\-\s]/g, '');
      }
    }

    return null;
  }

  /**
   * Extract line items (simplified)
   */
  extractLineItems(text) {
    // This would be more sophisticated in production
    const lines = text.split('\n');
    const items = [];

    for (const line of lines) {
      if (line.match(/[0-9]+[,.]?[0-9]*\s*(?:zł|PLN)/)) {
        items.push({
          description: line.replace(/[0-9]+[,.]?[0-9]*\s*(?:zł|PLN).*/, '').trim(),
          amount: this.extractAmount(line)
        });
      }
    }

    return items.slice(0, 10); // Limit to 10 items
  }

  /**
   * Calculate extraction confidence
   */
  calculateExtractionConfidence(text) {
    let confidence = 0;
    const requiredFields = ['faktura', 'kwota', 'data'];
    
    for (const field of requiredFields) {
      if (text.toLowerCase().includes(field)) {
        confidence += 33;
      }
    }

    return Math.min(confidence, 100);
  }

  /**
   * Normalize date format
   */
  normalizeDate(dateString) {
    try {
      const cleaned = dateString.replace(/[^\d.\/\-]/g, '');
      const parts = cleaned.split(/[.\/\-]/);
      
      if (parts.length === 3) {
        const [day, month, year] = parts;
        const fullYear = year.length === 2 ? `20${year}` : year;
        return `${fullYear}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
      }
      
      return dateString;
    } catch (error) {
      return dateString;
    }
  }

  /**
   * Clean extracted text
   */
  cleanText(text) {
    return text
      .replace(/\s+/g, ' ') // Multiple spaces to single space
      .replace(/\n\s*\n/g, '\n') // Multiple newlines to single newline
      .trim();
  }

  /**
   * Check if text is garbled (poor extraction)
   */
  isGarbledText(text) {
    const specialCharRatio = (text.match(/[^\w\s\n\t.,;:!?()\[\]{}\+\-\*/=@#$%^&|\\~`"'<>]/g) || []).length / text.length;
    return specialCharRatio > 0.3;
  }

  /**
   * Process multiple images/documents in batch
   */
  async batchExtractText(files) {
    try {
      logger.info(`🔄 Starting batch OCR for ${files.length} files...`);
      
      const results = [];
      
      for (let i = 0; i < files.length; i++) {
        const file = files[i];
        try {
          logger.info(`📄 Processing file ${i + 1}/${files.length}: ${file.name}`);
          
          const text = await this.extractText(file.buffer, file.mimeType);
          
          results.push({
            filename: file.name,
            success: true,
            text,
            extractedAt: new Date().toISOString()
          });
        } catch (error) {
          logger.error(`❌ Failed to process ${file.name}:`, error);
          
          results.push({
            filename: file.name,
            success: false,
            error: error.message,
            extractedAt: new Date().toISOString()
          });
        }
      }

      logger.info(`✅ Batch OCR completed: ${results.filter(r => r.success).length}/${files.length} successful`);
      return results;
    } catch (error) {
      logger.error('❌ Batch OCR failed:', error);
      throw error;
    }
  }

  /**
   * Health check
   */
  async healthCheck() {
    try {
      if (!this.initialized) {
        return { status: 'not_initialized' };
      }

      // Test OCR with a simple image
      const testBuffer = Buffer.from('iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==', 'base64');
      
      await this.tesseractWorker.recognize(testBuffer);
      
      return {
        status: 'healthy',
        initialized: this.initialized,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    try {
      if (this.tesseractWorker) {
        await this.tesseractWorker.terminate();
        this.tesseractWorker = null;
      }
      this.initialized = false;
      logger.info('✅ OCR service cleaned up');
    } catch (error) {
      logger.error('❌ OCR cleanup failed:', error);
    }
  }
}

module.exports = new OCRService();
