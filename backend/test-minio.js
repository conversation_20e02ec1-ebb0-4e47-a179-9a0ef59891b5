/**
 * 🧪 Test MinIO Connection - Quick Test Script
 */

const Minio = require('minio');

const testMinIO = async () => {
  try {
    console.log('🚀 Testing MinIO connection...');
    
    const client = new Minio.Client({
      endPoint: 'localhost',
      port: 9001,
      useSSL: false,
      accessKey: 'minioadmin',
      secretKey: 'minioadmin123',
    });

    console.log('📋 Listing buckets...');
    const buckets = await client.listBuckets();
    console.log('✅ Buckets:', buckets);

    console.log('🎉 MinIO connection successful!');
  } catch (error) {
    console.error('❌ MinIO connection failed:', error);
  }
};

testMinIO();
