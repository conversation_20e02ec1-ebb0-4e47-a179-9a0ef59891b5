import { useEffect, useState } from 'react';

import { Tag, Row, Col, Typography, Space } from 'antd';
import {
  ToolOutlined,
  ProjectOutlined,
  FunnelPlotOutlined,
  AlertOutlined,
  DashboardOutlined,
  FileTextOutlined,
  CalendarOutlined,
  TeamOutlined
} from '@ant-design/icons';
import useLanguage from '@/locale/useLanguage';

import { useMoney } from '@/settings';

import { request } from '@/request';
import useFetch from '@/hooks/useFetch';
import useOnFetch from '@/hooks/useOnFetch';

import RecentTable from './components/RecentTable';

import SummaryCard from './components/SummaryCard';
import PreviewCard from './components/PreviewCard';
import CustomerPreviewCard from './components/CustomerPreviewCard';
import AIInsights from '@/components/AIInsights';
import AIAnalytics from '@/components/AIAnalytics';
import EnhancedAI from '@/components/EnhancedAI';

// Cosmic Components
import CosmicCard from '@/components/cosmic/CosmicCard';
import CosmicSummaryCard from '@/components/cosmic/CosmicSummaryCard';

import { selectMoneyFormat } from '@/redux/settings/selectors';
import { useSelector } from 'react-redux';

const { Title, Text } = Typography;

export default function DashboardModule() {
  const translate = useLanguage();
  const { moneyFormatter } = useMoney();
  const money_format_settings = useSelector(selectMoneyFormat);

  const getStatsData = async ({ entity, currency }) => {
    return await request.summary({
      entity,
      options: { currency },
    });
  };

  const {
    result: invoiceResult,
    isLoading: invoiceLoading,
    onFetch: fetchInvoicesStats,
  } = useOnFetch();

  const { result: quoteResult, isLoading: quoteLoading, onFetch: fetchQuotesStats } = useOnFetch();

  const {
    result: paymentResult,
    isLoading: paymentLoading,
    onFetch: fetchPayemntsStats,
  } = useOnFetch();

  const { result: clientResult, isLoading: clientLoading } = useFetch(() =>
    request.summary({ entity: 'client' })
  );

  // HVAC-specific data
  const { result: equipmentResult, isLoading: equipmentLoading } = useFetch(() =>
    request.summary({ entity: 'equipment' })
  );

  const { result: serviceOrderResult, isLoading: serviceOrderLoading } = useFetch(() =>
    request.summary({ entity: 'serviceorder' })
  );

  const { result: opportunityResult, isLoading: opportunityLoading } = useFetch(() =>
    request.summary({ entity: 'opportunity' })
  );

  useEffect(() => {
    const currency = money_format_settings.default_currency_code || null;

    if (currency) {
      fetchInvoicesStats(getStatsData({ entity: 'invoice', currency }));
      fetchQuotesStats(getStatsData({ entity: 'quote', currency }));
      fetchPayemntsStats(getStatsData({ entity: 'payment', currency }));
    }
  }, [money_format_settings.default_currency_code]);

  const dataTableColumns = [
    {
      title: translate('number'),
      dataIndex: 'number',
    },
    {
      title: translate('Client'),
      dataIndex: ['client', 'name'],
    },

    {
      title: translate('Total'),
      dataIndex: 'total',
      onCell: () => {
        return {
          style: {
            textAlign: 'right',
            whiteSpace: 'nowrap',
            direction: 'ltr',
          },
        };
      },
      render: (total, record) => moneyFormatter({ amount: total, currency_code: record.currency }),
    },
    {
      title: translate('Status'),
      dataIndex: 'status',
    },
  ];

  const entityData = [
    {
      result: invoiceResult,
      isLoading: invoiceLoading,
      entity: 'invoice',
      title: translate('Invoices'),
    },
    {
      result: quoteResult,
      isLoading: quoteLoading,
      entity: 'quote',
      title: translate('quote'),
    },
  ];

  const statisticCards = entityData.map((data, index) => {
    const { result, entity, isLoading, title } = data;

    return (
      <PreviewCard
        key={index}
        title={title}
        isLoading={isLoading}
        entity={entity}
        statistics={
          !isLoading &&
          result?.performance?.map((item) => ({
            tag: item?.status,
            color: 'blue',
            value: item?.percentage,
          }))
        }
      />
    );
  });

  if (money_format_settings) {
    return (
      <>
        {/* Dashboard Header */}
        <div className="animate-fade-in" style={{ marginBottom: 'var(--space-lg)' }}>
          <Title level={2} style={{ margin: 0, color: 'var(--hvac-gray-800)' }}>
            Dashboard HVAC
          </Title>
          <Text type="secondary">
            Przegląd kluczowych wskaźników systemu zarządzania HVAC
          </Text>
        </div>

        {/* HVAC-specific metrics with cosmic styling */}
        <Row gutter={[32, 32]} className="animate-fade-in">
          <CosmicSummaryCard
            title="Aktywne Zlecenia"
            subtitle="Serwis w trakcie"
            prefix="W trakcie realizacji"
            isLoading={serviceOrderLoading}
            data={serviceOrderResult?.activeOrders || 0}
            icon={<ProjectOutlined />}
            status="primary"
            trend="up"
            trendValue="+12%"
          />
          <CosmicSummaryCard
            title="Sprzęt HVAC"
            subtitle="Rejestr urządzeń"
            prefix="Łącznie w systemie"
            isLoading={equipmentLoading}
            data={equipmentResult?.totalEquipment || 0}
            icon={<ToolOutlined />}
            status="success"
            trend="up"
            trendValue="+5%"
          />
          <CosmicSummaryCard
            title="Pipeline Sprzedaży"
            subtitle="Wartość możliwości"
            prefix="Wartość ważona"
            isLoading={opportunityLoading}
            data={opportunityResult?.totalPipelineValue || 0}
            icon={<FunnelPlotOutlined />}
            status="primary"
            trend="up"
            trendValue="+18%"
          />
          <CosmicSummaryCard
            title="Serwis Pilny"
            subtitle="Wymaga interwencji"
            prefix="Wymaga uwagi"
            isLoading={equipmentLoading}
            data={equipmentResult?.maintenanceDue || 0}
            icon={<AlertOutlined />}
            status="warning"
            trend="down"
            trendValue="-3%"
          />
        </Row>

        {/* Traditional ERP metrics with cosmic styling */}
        <div style={{ margin: 'var(--space-xl) 0 var(--space-lg) 0' }}>
          <Title level={3} style={{ color: 'var(--hvac-gray-700)', marginBottom: 'var(--space-md)' }}>
            Metryki Finansowe
          </Title>
        </div>
        <Row gutter={[32, 32]} className="animate-fade-in">
          <CosmicSummaryCard
            title={translate('Invoices')}
            subtitle="Faktury wystawione"
            prefix={translate('This month')}
            isLoading={invoiceLoading}
            data={invoiceResult?.total}
            icon={<FileTextOutlined />}
            status="primary"
          />
          <CosmicSummaryCard
            title={translate('Quote')}
            subtitle="Oferty przygotowane"
            prefix={translate('This month')}
            isLoading={quoteLoading}
            data={quoteResult?.total}
            icon={<DashboardOutlined />}
            status="success"
          />
          <CosmicSummaryCard
            title={translate('paid')}
            subtitle="Płatności otrzymane"
            prefix={translate('This month')}
            isLoading={paymentLoading}
            data={paymentResult?.total}
            icon={<CalendarOutlined />}
            status="success"
          />
          <CosmicSummaryCard
            title={translate('Unpaid')}
            subtitle="Zaległe płatności"
            prefix={translate('Not Paid')}
            isLoading={invoiceLoading}
            data={invoiceResult?.total_undue}
            icon={<AlertOutlined />}
            status="error"
          />
        </Row>
        <div className="space30"></div>
        <Row gutter={[32, 32]}>
          <Col className="gutter-row w-full" sm={{ span: 24 }} md={{ span: 24 }} lg={{ span: 18 }}>
            <div className="whiteBox shadow" style={{ height: 458 }}>
              <Row className="pad20" gutter={[0, 0]}>
                {statisticCards}
              </Row>
            </div>
          </Col>
          <Col className="gutter-row w-full" sm={{ span: 24 }} md={{ span: 24 }} lg={{ span: 6 }}>
            <CustomerPreviewCard
              isLoading={clientLoading}
              activeCustomer={clientResult?.active}
              newCustomer={clientResult?.new}
            />
          </Col>
        </Row>
        {/* Recent Activity Section */}
        <div style={{ margin: 'var(--space-xl) 0 var(--space-lg) 0' }}>
          <Title level={3} style={{ color: 'var(--hvac-gray-700)', marginBottom: 'var(--space-md)' }}>
            Ostatnia Aktywność
          </Title>
        </div>
        <Row gutter={[32, 32]} className="animate-fade-in">
          <Col className="gutter-row w-full" sm={{ span: 24 }} lg={{ span: 8 }}>
            <CosmicCard
              title={
                <Space>
                  <ProjectOutlined style={{ color: 'var(--hvac-primary)' }} />
                  Najnowsze Zlecenia
                </Space>
              }
              hoverable={true}
              style={{ height: '100%' }}
            >
              <RecentTable
                entity={'serviceorder'}
                dataTableColumns={[
                  { title: 'Numer', dataIndex: 'orderNumber' },
                  { title: 'Klient', dataIndex: ['client', 'name'] },
                  { title: 'Typ', dataIndex: 'type' },
                  { title: 'Etap', dataIndex: 'stage' }
                ]}
              />
            </CosmicCard>
          </Col>

          <Col className="gutter-row w-full" sm={{ span: 24 }} lg={{ span: 8 }}>
            <CosmicCard
              title={
                <Space>
                  <FunnelPlotOutlined style={{ color: 'var(--hvac-secondary)' }} />
                  Gorące Leady
                </Space>
              }
              hoverable={true}
              style={{ height: '100%' }}
            >
              <RecentTable
                entity={'opportunity'}
                dataTableColumns={[
                  { title: 'Nazwa', dataIndex: 'name' },
                  { title: 'Klient', dataIndex: ['client', 'name'] },
                  { title: 'Wartość', dataIndex: 'value' },
                  { title: 'Etap', dataIndex: 'stage' }
                ]}
              />
            </CosmicCard>
          </Col>

          <Col className="gutter-row w-full" sm={{ span: 24 }} lg={{ span: 8 }}>
            <CosmicCard
              title={
                <Space>
                  <ToolOutlined style={{ color: 'var(--hvac-warning)' }} />
                  Sprzęt do Serwisu
                </Space>
              }
              hoverable={true}
              style={{ height: '100%' }}
            >
              <RecentTable
                entity={'equipment'}
                dataTableColumns={[
                  { title: 'Nazwa', dataIndex: 'name' },
                  { title: 'Klient', dataIndex: ['client', 'name'] },
                  { title: 'Typ', dataIndex: 'type' },
                  { title: 'Następny serwis', dataIndex: 'nextMaintenanceDate' }
                ]}
              />
            </CosmicCard>
          </Col>
        </Row>

        {/* Financial Documents Section */}
        <div style={{ margin: 'var(--space-xl) 0 var(--space-lg) 0' }}>
          <Title level={3} style={{ color: 'var(--hvac-gray-700)', marginBottom: 'var(--space-md)' }}>
            Dokumenty Finansowe
          </Title>
        </div>
        <Row gutter={[32, 32]} className="animate-fade-in">
          <Col className="gutter-row w-full" sm={{ span: 24 }} lg={{ span: 12 }}>
            <CosmicCard
              title={
                <Space>
                  <FileTextOutlined style={{ color: 'var(--hvac-primary)' }} />
                  {translate('Recent Invoices')}
                </Space>
              }
              hoverable={true}
              style={{ height: '100%' }}
            >
              <RecentTable entity={'invoice'} dataTableColumns={dataTableColumns} />
            </CosmicCard>
          </Col>

          <Col className="gutter-row w-full" sm={{ span: 24 }} lg={{ span: 12 }}>
            <CosmicCard
              title={
                <Space>
                  <DashboardOutlined style={{ color: 'var(--hvac-success)' }} />
                  {translate('Recent Quotes')}
                </Space>
              }
              hoverable={true}
              style={{ height: '100%' }}
            >
              <RecentTable entity={'quote'} dataTableColumns={dataTableColumns} />
            </CosmicCard>
          </Col>
        </Row>

        {/* AI Intelligence Section */}
        <div style={{ margin: 'var(--space-xl) 0 var(--space-lg) 0' }}>
          <Title level={3} style={{ color: 'var(--hvac-gray-700)', marginBottom: 'var(--space-md)' }}>
            Inteligencja AI
          </Title>
          <Text type="secondary">
            Zaawansowane analizy i predykcje oparte na sztucznej inteligencji
          </Text>
        </div>
        <Row gutter={[32, 32]} className="animate-fade-in">
          <Col span={24}>
            <CosmicCard
              title="AI Insights - Wgląd w Dane"
              hoverable={true}
              gradient={true}
            >
              <AIInsights />
            </CosmicCard>
          </Col>
        </Row>

        <div style={{ margin: 'var(--space-lg) 0' }}>
          <Row gutter={[32, 32]} className="animate-fade-in">
            <Col span={24}>
              <CosmicCard
                title="AI Analytics - Analityka Predykcyjna"
                hoverable={true}
                gradient={true}
              >
                <AIAnalytics />
              </CosmicCard>
            </Col>
          </Row>
        </div>

        <div style={{ margin: 'var(--space-lg) 0' }}>
          <Row gutter={[32, 32]} className="animate-fade-in">
            <Col span={24}>
              <CosmicCard
                title="Enhanced AI - Zaawansowana Inteligencja"
                hoverable={true}
                gradient={true}
              >
                <EnhancedAI />
              </CosmicCard>
            </Col>
          </Row>
        </div>
      </>
    );
  } else {
    return <></>;
  }
}
