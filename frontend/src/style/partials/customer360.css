/**
 * 🚀 COSMIC CUSTOMER 360° DASHBOARD STYLES
 * 
 * <PERSON><PERSON><PERSON><PERSON><PERSON> profil klienta w Europie - cosmic-level design
 * Inspirowany Material Design 3 Expressive i golden ratio
 */

/* 🎨 CSS Variables for Customer 360° */
:root {
  /* Golden Ratio Spacing */
  --space-xs: 0.382rem;    /* φ^-2 */
  --space-sm: 0.618rem;    /* φ^-1 */
  --space-base: 1rem;      /* φ^0 */
  --space-md: 1.618rem;    /* φ^1 */
  --space-lg: 2.618rem;    /* φ^2 */
  --space-xl: 4.236rem;    /* φ^3 */
  --space-xxl: 6.854rem;   /* φ^4 */

  /* HVAC Color Palette */
  --hvac-primary: #1890ff;
  --hvac-secondary: #722ed1;
  --hvac-success: #52c41a;
  --hvac-warning: #faad14;
  --hvac-error: #ff4d4f;
  --hvac-white: #ffffff;
  --hvac-gray-50: #fafafa;
  --hvac-gray-100: #f5f5f5;
  --hvac-gray-200: #f0f0f0;
  --hvac-gray-300: #d9d9d9;
  --hvac-gray-500: #8c8c8c;
  --hvac-gray-600: #595959;
  --hvac-gray-700: #434343;
  --hvac-gray-800: #262626;

  /* Typography Scale */
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-md: 1.125rem;
  --font-size-lg: 1.25rem;
  --font-size-xl: 1.5rem;
  --font-size-xxl: 2rem;

  /* Border Radius */
  --radius-base: 6px;
  --radius-lg: 12px;
  --radius-xl: 18px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-base: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* 🎭 Cosmic Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* 🎨 Animation Classes */
.animate-fade-in {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slide-in {
  animation: slideIn 0.6s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-pulse {
  animation: pulse 2s infinite;
}

.animate-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

/* 🚀 Cosmic Button Styles */
.cosmic-btn {
  border-radius: var(--radius-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.cosmic-btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.cosmic-btn-primary {
  background: linear-gradient(135deg, var(--hvac-primary), #40a9ff);
  border: none;
  color: white;
  border-radius: var(--radius-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.cosmic-btn-primary:hover {
  background: linear-gradient(135deg, #40a9ff, var(--hvac-primary));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

.cosmic-btn-secondary {
  background: linear-gradient(135deg, var(--hvac-secondary), #9254de);
  border: none;
  color: white;
  border-radius: var(--radius-base);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--shadow-sm);
}

.cosmic-btn-secondary:hover {
  background: linear-gradient(135deg, #9254de, var(--hvac-secondary));
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
}

/* 🎯 Modal & Drawer Cosmic Styles */
.cosmic-modal .ant-modal-content {
  border-radius: var(--radius-lg);
  overflow: hidden;
  box-shadow: var(--shadow-lg);
}

.cosmic-modal .ant-modal-header {
  background: linear-gradient(135deg, var(--hvac-primary), #40a9ff);
  color: white;
  border-bottom: none;
  padding: var(--space-md);
}

.cosmic-modal .ant-modal-title {
  color: white;
  font-weight: 600;
}

.cosmic-drawer .ant-drawer-header {
  background: linear-gradient(135deg, var(--hvac-primary), #40a9ff);
  color: white;
  border-bottom: none;
  padding: var(--space-md);
}

.cosmic-drawer .ant-drawer-title {
  color: white;
  font-weight: 600;
}

/* 📱 Responsive Design */
@media (max-width: 768px) {
  :root {
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-base: 0.75rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-xxl: 3rem;
  }

  .cosmic-modal {
    margin: 0;
    max-width: 100vw;
  }

  .cosmic-drawer {
    width: 100vw !important;
  }
}

/* 🎨 Micro-interactions */
.cosmic-card-hover {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.cosmic-card-hover:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg);
}

/* 🌟 Loading States */
.cosmic-loading {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
  border-radius: var(--radius-base);
}

/* 🎯 Focus States */
.cosmic-focus:focus {
  outline: 2px solid var(--hvac-primary);
  outline-offset: 2px;
}

/* 🚀 Success States */
.cosmic-success {
  background: linear-gradient(135deg, var(--hvac-success), #73d13d);
  color: white;
}

/* ⚠️ Warning States */
.cosmic-warning {
  background: linear-gradient(135deg, var(--hvac-warning), #ffc53d);
  color: white;
}

/* ❌ Error States */
.cosmic-error {
  background: linear-gradient(135deg, var(--hvac-error), #ff7875);
  color: white;
}
