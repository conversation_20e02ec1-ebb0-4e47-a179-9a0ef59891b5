/**
 * 🧠 INTELLIGENT CALENDAR DEMO PAGE
 * 
 * Demo page dla najinteligentniejszego kalendarza HVAC w Europie
 * z AI features, voice commands i cosmic-level UX
 */

import React, { useState } from 'react';
import { 
  Button, 
  Space, 
  Card, 
  Typography, 
  Row, 
  Col, 
  Select,
  Divider,
  Tag,
  Alert,
  Switch,
  Tooltip
} from 'antd';
import {
  RobotOutlined,
  CalendarOutlined,
  ThunderboltOutlined,
  CloudOutlined,
  SoundOutlined,
  StarOutlined,
  BulbOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined
} from '@ant-design/icons';

import { IntelligentCalendar } from '../components/Calendar';
import { CalendarDashboard } from '../components/Calendar';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const IntelligentCalendarDemo = () => {
  const [calendarMode, setCalendarMode] = useState('intelligent');
  const [aiFeatures, setAiFeatures] = useState({
    voiceCommands: true,
    weatherIntelligence: true,
    routeOptimization: true,
    conflictDetection: true,
    predictiveScheduling: true
  });

  const handleFeatureToggle = (feature) => {
    setAiFeatures(prev => ({
      ...prev,
      [feature]: !prev[feature]
    }));
  };

  const renderFeatureControls = () => (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
          <BulbOutlined style={{ color: 'var(--hvac-primary)' }} />
          🎛️ AI Features Control
        </div>
      }
      style={{ marginBottom: 'var(--space-lg)' }}
    >
      <Row gutter={[16, 16]}>
        <Col xs={24} sm={12} lg={8}>
          <div style={{ 
            padding: 'var(--space-sm)', 
            background: 'var(--hvac-gray-50)', 
            borderRadius: 'var(--radius-base)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <Text strong>🎤 Voice Commands</Text>
              <div>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                  Voice control for boss
                </Text>
              </div>
            </div>
            <Switch 
              checked={aiFeatures.voiceCommands}
              onChange={() => handleFeatureToggle('voiceCommands')}
            />
          </div>
        </Col>
        
        <Col xs={24} sm={12} lg={8}>
          <div style={{ 
            padding: 'var(--space-sm)', 
            background: 'var(--hvac-gray-50)', 
            borderRadius: 'var(--radius-base)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <Text strong>🌤️ Weather Intelligence</Text>
              <div>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                  Weather-based scheduling
                </Text>
              </div>
            </div>
            <Switch 
              checked={aiFeatures.weatherIntelligence}
              onChange={() => handleFeatureToggle('weatherIntelligence')}
            />
          </div>
        </Col>
        
        <Col xs={24} sm={12} lg={8}>
          <div style={{ 
            padding: 'var(--space-sm)', 
            background: 'var(--hvac-gray-50)', 
            borderRadius: 'var(--radius-base)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <Text strong>🚀 Route Optimization</Text>
              <div>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                  AI route planning
                </Text>
              </div>
            </div>
            <Switch 
              checked={aiFeatures.routeOptimization}
              onChange={() => handleFeatureToggle('routeOptimization')}
            />
          </div>
        </Col>
        
        <Col xs={24} sm={12} lg={8}>
          <div style={{ 
            padding: 'var(--space-sm)', 
            background: 'var(--hvac-gray-50)', 
            borderRadius: 'var(--radius-base)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <Text strong>⚠️ Conflict Detection</Text>
              <div>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                  Smart scheduling conflicts
                </Text>
              </div>
            </div>
            <Switch 
              checked={aiFeatures.conflictDetection}
              onChange={() => handleFeatureToggle('conflictDetection')}
            />
          </div>
        </Col>
        
        <Col xs={24} sm={12} lg={8}>
          <div style={{ 
            padding: 'var(--space-sm)', 
            background: 'var(--hvac-gray-50)', 
            borderRadius: 'var(--radius-base)',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <div>
              <Text strong>🔮 Predictive Scheduling</Text>
              <div>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                  AI predicts best times
                </Text>
              </div>
            </div>
            <Switch 
              checked={aiFeatures.predictiveScheduling}
              onChange={() => handleFeatureToggle('predictiveScheduling')}
            />
          </div>
        </Col>
      </Row>
    </Card>
  );

  const renderModeSelector = () => (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
          <CalendarOutlined style={{ color: 'var(--hvac-secondary)' }} />
          📅 Calendar Mode
        </div>
      }
      style={{ marginBottom: 'var(--space-lg)' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Choose calendar mode:</Text>
        </div>
        <Space wrap>
          <Button 
            type={calendarMode === 'intelligent' ? 'primary' : 'default'}
            onClick={() => setCalendarMode('intelligent')}
            icon={<RobotOutlined />}
            className={calendarMode === 'intelligent' ? 'cosmic-btn-primary' : 'cosmic-btn'}
          >
            🧠 Intelligent Calendar
          </Button>
          <Button 
            type={calendarMode === 'standard' ? 'primary' : 'default'}
            onClick={() => setCalendarMode('standard')}
            icon={<CalendarOutlined />}
            className={calendarMode === 'standard' ? 'cosmic-btn-primary' : 'cosmic-btn'}
          >
            📅 Standard Calendar
          </Button>
        </Space>
      </Space>
    </Card>
  );

  return (
    <div style={{ 
      padding: 'var(--space-lg)', 
      background: 'var(--hvac-gray-50)', 
      minHeight: '100vh' 
    }}>
      {/* Header */}
      <div style={{ marginBottom: 'var(--space-xl)' }}>
        <Title level={1} style={{ 
          color: 'var(--hvac-primary)', 
          textAlign: 'center',
          marginBottom: 'var(--space-sm)'
        }}>
          🧠 Intelligent Calendar Demo
        </Title>
        <Paragraph style={{ 
          textAlign: 'center', 
          fontSize: 'var(--font-size-lg)',
          color: 'var(--hvac-gray-600)'
        }}>
          Najinteligentniejszy kalendarz HVAC w Europie z AI features
        </Paragraph>
        
        <Alert
          message="🚀 Revolutionary AI Features"
          description="Voice commands, weather intelligence, route optimization, conflict detection, and predictive scheduling - all powered by AI!"
          type="info"
          showIcon
          style={{ marginTop: 'var(--space-md)' }}
        />
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={8}>
          {renderModeSelector()}
          {renderFeatureControls()}
          
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <StarOutlined style={{ color: 'var(--hvac-success)' }} />
                ✨ AI Features
              </div>
            }
          >
            <Space direction="vertical" size="small">
              <Tag color="blue" icon={<RobotOutlined />}>🧠 AI Assistant</Tag>
              <Tag color="green" icon={<SoundOutlined />}>🎤 Voice Commands</Tag>
              <Tag color="orange" icon={<CloudOutlined />}>🌤️ Weather Intelligence</Tag>
              <Tag color="purple" icon={<ThunderboltOutlined />}>🚀 Route Optimization</Tag>
              <Tag color="red" icon={<ClockCircleOutlined />}>⚠️ Conflict Detection</Tag>
              <Tag color="cyan" icon={<EnvironmentOutlined />}>🔮 Predictive Scheduling</Tag>
            </Space>
            
            <Divider />
            
            <div>
              <Text strong style={{ color: 'var(--hvac-primary)' }}>
                🎯 Voice Commands Examples:
              </Text>
              <div style={{ marginTop: 'var(--space-sm)' }}>
                <Text type="secondary" style={{ fontSize: 'var(--font-size-sm)' }}>
                  • "Pokaż kalendarz"<br/>
                  • "Optymalizuj trasy"<br/>
                  • "Nowe wydarzenie"<br/>
                  • "Sprawdź pogodę"
                </Text>
              </div>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          <div style={{ 
            background: 'white', 
            borderRadius: 'var(--radius-lg)',
            overflow: 'hidden',
            boxShadow: 'var(--shadow-lg)'
          }}>
            {calendarMode === 'intelligent' ? (
              <IntelligentCalendar />
            ) : (
              <CalendarDashboard />
            )}
          </div>
        </Col>
      </Row>
    </div>
  );
};

export default IntelligentCalendarDemo;
