/**
 * 🚀 CUSTOMER 360° DEMO PAGE
 * 
 * Demo page do testowania cosmic-level profilu klienta 360°
 * <PERSON><PERSON><PERSON><PERSON><PERSON> interfejs CRM w Europie!
 */

import React, { useState } from 'react';
import { 
  Button, 
  Space, 
  Card, 
  Typography, 
  Row, 
  Col, 
  Select,
  Divider,
  Tag,
  Alert
} from 'antd';
import {
  UserOutlined,
  EyeOutlined,
  SettingOutlined,
  ThunderboltOutlined,
  StarOutlined
} from '@ant-design/icons';

import EnhancedCustomer360Dashboard, { 
  Customer360Modal, 
  Customer360Drawer, 
  Customer360Page 
} from '../components/EnhancedCustomer360Dashboard';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const Customer360Demo = () => {
  const [modalVisible, setModalVisible] = useState(false);
  const [drawerVisible, setDrawerVisible] = useState(false);
  const [selectedCustomerId, setSelectedCustomerId] = useState('demo-customer-1');
  const [displayMode, setDisplayMode] = useState('component');

  // Demo customer IDs
  const demoCustomers = [
    { id: 'demo-customer-1', name: '<PERSON> - <PERSON>zkanie 85m²', type: 'Mieszkaniowy' },
    { id: 'demo-customer-2', name: 'Firma ABC Sp. z o.o.', type: 'Biznesowy' },
    { id: 'demo-customer-3', name: 'Maria Nowak - Dom 150m²', type: 'Mieszkaniowy' },
    { id: 'demo-customer-4', name: 'Hotel Warszawski', type: 'Komercyjny' }
  ];

  const handleCustomerChange = (customerId) => {
    setSelectedCustomerId(customerId);
  };

  const renderModeSelector = () => (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
          <SettingOutlined style={{ color: 'var(--hvac-primary)' }} />
          🎛️ Tryb Wyświetlania
        </div>
      }
      style={{ marginBottom: 'var(--space-lg)' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Wybierz tryb wyświetlania profilu 360°:</Text>
        </div>
        <Space wrap>
          <Button 
            type={displayMode === 'component' ? 'primary' : 'default'}
            onClick={() => setDisplayMode('component')}
            icon={<EyeOutlined />}
          >
            Komponent
          </Button>
          <Button 
            type={displayMode === 'modal' ? 'primary' : 'default'}
            onClick={() => {
              setDisplayMode('modal');
              setModalVisible(true);
            }}
            icon={<EyeOutlined />}
          >
            Modal
          </Button>
          <Button 
            type={displayMode === 'drawer' ? 'primary' : 'default'}
            onClick={() => {
              setDisplayMode('drawer');
              setDrawerVisible(true);
            }}
            icon={<EyeOutlined />}
          >
            Drawer
          </Button>
          <Button 
            type={displayMode === 'page' ? 'primary' : 'default'}
            onClick={() => setDisplayMode('page')}
            icon={<EyeOutlined />}
          >
            Pełna Strona
          </Button>
        </Space>
      </Space>
    </Card>
  );

  const renderCustomerSelector = () => (
    <Card 
      title={
        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
          <UserOutlined style={{ color: 'var(--hvac-secondary)' }} />
          👥 Wybór Klienta Demo
        </div>
      }
      style={{ marginBottom: 'var(--space-lg)' }}
    >
      <Space direction="vertical" style={{ width: '100%' }}>
        <div>
          <Text strong>Wybierz klienta do analizy:</Text>
        </div>
        <Select
          value={selectedCustomerId}
          onChange={handleCustomerChange}
          style={{ width: '100%' }}
          size="large"
        >
          {demoCustomers.map(customer => (
            <Option key={customer.id} value={customer.id}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>{customer.name}</span>
                <Tag color="blue">{customer.type}</Tag>
              </div>
            </Option>
          ))}
        </Select>
      </Space>
    </Card>
  );

  if (displayMode === 'page') {
    return <Customer360Page customerId={selectedCustomerId} />;
  }

  return (
    <div style={{ 
      padding: 'var(--space-lg)', 
      background: 'var(--hvac-gray-50)', 
      minHeight: '100vh' 
    }}>
      {/* Header */}
      <div style={{ marginBottom: 'var(--space-xl)' }}>
        <Title level={1} style={{ 
          color: 'var(--hvac-primary)', 
          textAlign: 'center',
          marginBottom: 'var(--space-sm)'
        }}>
          🚀 Customer 360° Demo
        </Title>
        <Paragraph style={{ 
          textAlign: 'center', 
          fontSize: 'var(--font-size-lg)',
          color: 'var(--hvac-gray-600)'
        }}>
          Najlepszy profil klienta w Europie - cosmic-level UX dla szefów HVAC
        </Paragraph>
        
        <Alert
          message="🎯 Demo Features"
          description="Ten demo pokazuje wszystkie funkcje profilu 360° klienta z AI insights, predykcyjną analityką i cosmic-level design."
          type="info"
          showIcon
          style={{ marginTop: 'var(--space-md)' }}
        />
      </div>

      <Row gutter={[24, 24]}>
        <Col xs={24} lg={8}>
          {renderModeSelector()}
          {renderCustomerSelector()}
          
          <Card 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <StarOutlined style={{ color: 'var(--hvac-success)' }} />
                ✨ Features
              </div>
            }
          >
            <Space direction="vertical" size="small">
              <Tag color="green">🧠 AI Predictive Analytics</Tag>
              <Tag color="blue">📊 Real-time Updates</Tag>
              <Tag color="purple">🎨 Cosmic Design</Tag>
              <Tag color="orange">📱 Mobile Responsive</Tag>
              <Tag color="red">⚡ Business Intelligence</Tag>
              <Tag color="cyan">🔧 HVAC Specific</Tag>
            </Space>
          </Card>
        </Col>

        <Col xs={24} lg={16}>
          {displayMode === 'component' && (
            <div style={{ 
              background: 'white', 
              borderRadius: 'var(--radius-lg)',
              overflow: 'hidden',
              boxShadow: 'var(--shadow-lg)'
            }}>
              <EnhancedCustomer360Dashboard 
                customerId={selectedCustomerId}
                mode="component"
              />
            </div>
          )}
        </Col>
      </Row>

      {/* Modal */}
      <Customer360Modal
        visible={modalVisible}
        onClose={() => setModalVisible(false)}
        customerId={selectedCustomerId}
      />

      {/* Drawer */}
      <Customer360Drawer
        visible={drawerVisible}
        onClose={() => setDrawerVisible(false)}
        customerId={selectedCustomerId}
        placement="right"
      />
    </div>
  );
};

export default Customer360Demo;
