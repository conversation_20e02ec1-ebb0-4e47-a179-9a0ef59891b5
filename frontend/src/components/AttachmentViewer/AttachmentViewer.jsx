/**
 * 📎 AttachmentViewer - Enhanced Email Attachment Management Component
 * Professional attachment visualization with corporate-level UX
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Table,
  Button,
  Tag,
  Space,
  Input,
  Select,
  Upload,
  Modal,
  Progress,
  Tooltip,
  Badge,
  Row,
  Col,
  Statistic,
  Typography,
  Divider,
  message,
  Spin,
  Image,
  Descriptions
} from 'antd';
import {
  FileOutlined,
  DownloadOutlined,
  EyeOutlined,
  DeleteOutlined,
  UploadOutlined,
  SearchOutlined,
  FilterOutlined,
  ReloadOutlined,
  PlayCircleOutlined,
  FileImageOutlined,
  FilePdfOutlined,
  FileTextOutlined,
  SoundOutlined,
  FileExcelOutlined,
  InboxOutlined
} from '@ant-design/icons';
import { CosmicCard, CosmicSummaryCard } from '../cosmic';
import './AttachmentViewer.css';

const { Search } = Input;
const { Option } = Select;
const { Title, Text } = Typography;
const { Dragger } = Upload;

const AttachmentViewer = ({ customerId = null, embedded = false }) => {
  const [attachments, setAttachments] = useState([]);
  const [loading, setLoading] = useState(false);
  const [uploading, setUploading] = useState(false);
  const [selectedAttachment, setSelectedAttachment] = useState(null);
  const [previewVisible, setPreviewVisible] = useState(false);
  const [uploadModalVisible, setUploadModalVisible] = useState(false);
  const [filters, setFilters] = useState({
    search: '',
    category: '',
    type: '',
    processingStatus: ''
  });
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 20,
    total: 0
  });
  const [stats, setStats] = useState({
    totalCount: 0,
    totalSize: 0,
    byStatus: [],
    byType: []
  });

  useEffect(() => {
    fetchAttachments();
    fetchStats();
  }, [filters, pagination.current, customerId]);

  const fetchAttachments = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        page: pagination.current,
        limit: pagination.pageSize,
        ...filters
      });

      if (customerId) {
        params.append('customerId', customerId);
      }

      const response = await fetch(`/api/attachments?${params}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setAttachments(data.data);
        setPagination(prev => ({
          ...prev,
          total: data.pagination.total
        }));
      } else {
        message.error('Failed to fetch attachments');
      }
    } catch (error) {
      console.error('Error fetching attachments:', error);
      message.error('Error fetching attachments');
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      const response = await fetch('/api/attachments/stats/overview', {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setStats(data.data);
      }
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const handleUpload = async (file) => {
    setUploading(true);
    try {
      const formData = new FormData();
      formData.append('file', file);
      if (customerId) {
        formData.append('customerId', customerId);
      }
      formData.append('category', 'general');

      const response = await fetch('/api/attachments/upload', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        },
        body: formData
      });

      const data = await response.json();

      if (data.success) {
        message.success('File uploaded successfully');
        setUploadModalVisible(false);
        fetchAttachments();
        fetchStats();
      } else {
        message.error(data.message || 'Upload failed');
      }
    } catch (error) {
      console.error('Upload error:', error);
      message.error('Upload failed');
    } finally {
      setUploading(false);
    }
  };

  const handleDownload = async (attachment) => {
    try {
      const response = await fetch(`/api/attachments/${attachment._id}/download`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (response.ok) {
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = attachment.originalName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
      } else {
        message.error('Download failed');
      }
    } catch (error) {
      console.error('Download error:', error);
      message.error('Download failed');
    }
  };

  const handleProcess = async (attachment) => {
    try {
      const response = await fetch(`/api/attachments/${attachment._id}/process`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        message.success('Processing started');
        fetchAttachments();
      } else {
        message.error(data.message || 'Processing failed');
      }
    } catch (error) {
      console.error('Processing error:', error);
      message.error('Processing failed');
    }
  };

  const handleDelete = async (attachment) => {
    Modal.confirm({
      title: 'Delete Attachment',
      content: `Are you sure you want to delete "${attachment.originalName}"?`,
      okText: 'Delete',
      okType: 'danger',
      onOk: async () => {
        try {
          const response = await fetch(`/api/attachments/${attachment._id}`, {
            method: 'DELETE',
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`
            }
          });

          const data = await response.json();

          if (data.success) {
            message.success('Attachment deleted');
            fetchAttachments();
            fetchStats();
          } else {
            message.error(data.message || 'Delete failed');
          }
        } catch (error) {
          console.error('Delete error:', error);
          message.error('Delete failed');
        }
      }
    });
  };

  const getFileIcon = (type, mimeType) => {
    switch (type) {
      case 'audio':
        return <SoundOutlined style={{ color: '#722ed1' }} />;
      case 'images':
        return <FileImageOutlined style={{ color: '#52c41a' }} />;
      case 'documents':
        if (mimeType === 'application/pdf') {
          return <FilePdfOutlined style={{ color: '#f5222d' }} />;
        }
        return <FileTextOutlined style={{ color: '#1890ff' }} />;
      case 'spreadsheets':
        return <FileExcelOutlined style={{ color: '#52c41a' }} />;
      default:
        return <FileOutlined style={{ color: '#8c8c8c' }} />;
    }
  };

  const getStatusColor = (status) => {
    const colors = {
      uploaded: 'blue',
      processing: 'orange',
      processed: 'green',
      ready_for_transcription: 'purple',
      transcribed: 'cyan',
      analyzed: 'green',
      error: 'red'
    };
    return colors[status] || 'default';
  };

  const getCategoryColor = (category) => {
    const colors = {
      invoice: 'gold',
      contract: 'blue',
      protocol: 'green',
      transcription: 'purple',
      image: 'cyan',
      document: 'geekblue',
      general: 'default'
    };
    return colors[category] || 'default';
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const columns = [
    {
      title: 'File',
      dataIndex: 'filename',
      key: 'filename',
      render: (text, record) => (
        <Space>
          {getFileIcon(record.type, record.mimeType)}
          <div>
            <div style={{ fontWeight: 500 }}>{record.originalName}</div>
            <Text type="secondary" style={{ fontSize: '12px' }}>
              {formatFileSize(record.size)}
            </Text>
          </div>
        </Space>
      )
    },
    {
      title: 'Category',
      dataIndex: 'category',
      key: 'category',
      render: (category) => (
        <Tag color={getCategoryColor(category)}>
          {category.toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Status',
      dataIndex: 'processingStatus',
      key: 'processingStatus',
      render: (status) => (
        <Tag color={getStatusColor(status)}>
          {status.replace('_', ' ').toUpperCase()}
        </Tag>
      )
    },
    {
      title: 'Email Info',
      key: 'emailInfo',
      render: (_, record) => (
        record.emailInfo ? (
          <div>
            <div style={{ fontSize: '12px' }}>
              <strong>From:</strong> {record.emailInfo.from}
            </div>
            <div style={{ fontSize: '12px' }}>
              <strong>Subject:</strong> {record.emailInfo.subject?.substring(0, 30)}...
            </div>
          </div>
        ) : (
          <Text type="secondary">Manual upload</Text>
        )
      )
    },
    {
      title: 'Uploaded',
      dataIndex: 'uploadedAt',
      key: 'uploadedAt',
      render: (date) => new Date(date).toLocaleDateString()
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_, record) => (
        <Space>
          <Tooltip title="Preview">
            <Button
              type="text"
              icon={<EyeOutlined />}
              onClick={() => {
                setSelectedAttachment(record);
                setPreviewVisible(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Download">
            <Button
              type="text"
              icon={<DownloadOutlined />}
              onClick={() => handleDownload(record)}
            />
          </Tooltip>
          {record.processingStatus === 'uploaded' && (
            <Tooltip title="Process">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                onClick={() => handleProcess(record)}
              />
            </Tooltip>
          )}
          <Tooltip title="Delete">
            <Button
              type="text"
              danger
              icon={<DeleteOutlined />}
              onClick={() => handleDelete(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ];

  const renderStats = () => (
    <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
      <Col xs={24} sm={12} md={6}>
        <CosmicSummaryCard
          title="Total Files"
          value={stats.totalCount}
          icon={<FileOutlined />}
          color="#1890ff"
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <CosmicSummaryCard
          title="Total Size"
          value={formatFileSize(stats.totalSize)}
          icon={<InboxOutlined />}
          color="#52c41a"
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <CosmicSummaryCard
          title="Processed"
          value={stats.byStatus.find(s => s._id === 'processed')?.count || 0}
          icon={<PlayCircleOutlined />}
          color="#722ed1"
        />
      </Col>
      <Col xs={24} sm={12} md={6}>
        <CosmicSummaryCard
          title="Pending"
          value={stats.byStatus.find(s => s._id === 'uploaded')?.count || 0}
          icon={<ReloadOutlined />}
          color="#fa8c16"
        />
      </Col>
    </Row>
  );

  return (
    <div className="attachment-viewer">
      {!embedded && renderStats()}

      <CosmicCard title="📎 Attachment Management" className="attachment-card">
        <div className="attachment-controls">
          <Row gutter={[16, 16]} align="middle">
            <Col xs={24} sm={12} md={8}>
              <Search
                placeholder="Search attachments..."
                value={filters.search}
                onChange={(e) => setFilters(prev => ({ ...prev, search: e.target.value }))}
                onSearch={fetchAttachments}
                enterButton={<SearchOutlined />}
              />
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Select
                placeholder="Category"
                value={filters.category}
                onChange={(value) => setFilters(prev => ({ ...prev, category: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="invoice">Invoice</Option>
                <Option value="contract">Contract</Option>
                <Option value="protocol">Protocol</Option>
                <Option value="transcription">Transcription</Option>
                <Option value="image">Image</Option>
                <Option value="document">Document</Option>
                <Option value="general">General</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Select
                placeholder="Type"
                value={filters.type}
                onChange={(value) => setFilters(prev => ({ ...prev, type: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="audio">Audio</Option>
                <Option value="documents">Documents</Option>
                <Option value="images">Images</Option>
                <Option value="spreadsheets">Spreadsheets</Option>
                <Option value="invoices">Invoices</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Select
                placeholder="Status"
                value={filters.processingStatus}
                onChange={(value) => setFilters(prev => ({ ...prev, processingStatus: value }))}
                style={{ width: '100%' }}
                allowClear
              >
                <Option value="uploaded">Uploaded</Option>
                <Option value="processing">Processing</Option>
                <Option value="processed">Processed</Option>
                <Option value="error">Error</Option>
              </Select>
            </Col>
            <Col xs={24} sm={12} md={4}>
              <Space>
                <Button
                  icon={<ReloadOutlined />}
                  onClick={fetchAttachments}
                  loading={loading}
                >
                  Refresh
                </Button>
                <Button
                  type="primary"
                  icon={<UploadOutlined />}
                  onClick={() => setUploadModalVisible(true)}
                >
                  Upload
                </Button>
              </Space>
            </Col>
          </Row>
        </div>

        <Table
          columns={columns}
          dataSource={attachments}
          rowKey="_id"
          loading={loading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total, range) => 
              `${range[0]}-${range[1]} of ${total} attachments`,
            onChange: (page, pageSize) => {
              setPagination(prev => ({
                ...prev,
                current: page,
                pageSize
              }));
            }
          }}
          scroll={{ x: 1200 }}
        />
      </CosmicCard>

      {/* Upload Modal */}
      <Modal
        title="Upload Attachment"
        open={uploadModalVisible}
        onCancel={() => setUploadModalVisible(false)}
        footer={null}
        width={600}
      >
        <Dragger
          name="file"
          multiple={false}
          beforeUpload={(file) => {
            handleUpload(file);
            return false;
          }}
          disabled={uploading}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            Click or drag file to this area to upload
          </p>
          <p className="ant-upload-hint">
            Support for single file upload. Maximum file size: 50MB
          </p>
        </Dragger>
        {uploading && (
          <div style={{ marginTop: 16 }}>
            <Spin /> Uploading...
          </div>
        )}
      </Modal>

      {/* Preview Modal */}
      <Modal
        title={`Preview: ${selectedAttachment?.originalName}`}
        open={previewVisible}
        onCancel={() => setPreviewVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAttachment && (
          <div>
            <Descriptions bordered size="small">
              <Descriptions.Item label="File Name">
                {selectedAttachment.originalName}
              </Descriptions.Item>
              <Descriptions.Item label="Size">
                {formatFileSize(selectedAttachment.size)}
              </Descriptions.Item>
              <Descriptions.Item label="Type">
                {selectedAttachment.type}
              </Descriptions.Item>
              <Descriptions.Item label="Category">
                <Tag color={getCategoryColor(selectedAttachment.category)}>
                  {selectedAttachment.category}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Status">
                <Tag color={getStatusColor(selectedAttachment.processingStatus)}>
                  {selectedAttachment.processingStatus}
                </Tag>
              </Descriptions.Item>
              <Descriptions.Item label="Uploaded">
                {new Date(selectedAttachment.uploadedAt).toLocaleString()}
              </Descriptions.Item>
            </Descriptions>

            {selectedAttachment.type === 'images' && selectedAttachment.publicUrl && (
              <div style={{ marginTop: 16, textAlign: 'center' }}>
                <Image
                  src={selectedAttachment.publicUrl}
                  alt={selectedAttachment.originalName}
                  style={{ maxWidth: '100%', maxHeight: 400 }}
                />
              </div>
            )}

            {selectedAttachment.aiAnalysis && (
              <div style={{ marginTop: 16 }}>
                <Title level={5}>AI Analysis</Title>
                {selectedAttachment.aiAnalysis.extractedText && (
                  <div>
                    <Text strong>Extracted Text:</Text>
                    <div style={{ 
                      background: '#f5f5f5', 
                      padding: 12, 
                      borderRadius: 4,
                      marginTop: 8,
                      maxHeight: 200,
                      overflow: 'auto'
                    }}>
                      {selectedAttachment.aiAnalysis.extractedText}
                    </div>
                  </div>
                )}
                {selectedAttachment.aiAnalysis.isInvoice && (
                  <div style={{ marginTop: 12 }}>
                    <Tag color="gold">Invoice Detected</Tag>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </Modal>
    </div>
  );
};

export default AttachmentViewer;
