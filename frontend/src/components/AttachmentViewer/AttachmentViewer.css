/**
 * 📎 AttachmentViewer Styles - Corporate Level Design
 * Professional styling for attachment management interface
 */

.attachment-viewer {
  padding: 0;
  background: transparent;
}

.attachment-card {
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
  border: 1px solid #f0f0f0;
  overflow: hidden;
}

.attachment-controls {
  padding: 20px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
  margin: -24px -24px 24px -24px;
}

.attachment-controls .ant-row {
  align-items: center;
}

.attachment-controls .ant-input-search {
  border-radius: 8px;
}

.attachment-controls .ant-select {
  border-radius: 8px;
}

.attachment-controls .ant-btn {
  border-radius: 8px;
  height: 40px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.attachment-controls .ant-btn-primary {
  background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
  border: none;
  box-shadow: 0 2px 8px rgba(24, 144, 255, 0.3);
  transition: all 0.3s ease;
}

.attachment-controls .ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
}

/* Table Enhancements */
.attachment-viewer .ant-table {
  border-radius: 8px;
  overflow: hidden;
}

.attachment-viewer .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  border-bottom: 2px solid #e8e8e8;
  font-weight: 600;
  color: #262626;
  padding: 16px 12px;
}

.attachment-viewer .ant-table-tbody > tr {
  transition: all 0.2s ease;
}

.attachment-viewer .ant-table-tbody > tr:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.attachment-viewer .ant-table-tbody > tr > td {
  padding: 16px 12px;
  border-bottom: 1px solid #f5f5f5;
}

/* File Icon Styling */
.attachment-viewer .anticon {
  font-size: 18px;
  margin-right: 8px;
}

/* Tag Enhancements */
.attachment-viewer .ant-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 11px;
  padding: 2px 8px;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* Action Buttons */
.attachment-viewer .ant-btn[data-icon] {
  border: none;
  background: transparent;
  color: #8c8c8c;
  transition: all 0.2s ease;
  border-radius: 6px;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.attachment-viewer .ant-btn[data-icon]:hover {
  background: #f0f0f0;
  color: #1890ff;
  transform: scale(1.1);
}

.attachment-viewer .ant-btn[data-icon].ant-btn-dangerous:hover {
  background: #fff2f0;
  color: #ff4d4f;
}

/* Upload Modal Enhancements */
.ant-upload-drag {
  border: 2px dashed #d9d9d9 !important;
  border-radius: 12px !important;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%) !important;
  transition: all 0.3s ease !important;
}

.ant-upload-drag:hover {
  border-color: #1890ff !important;
  background: linear-gradient(135deg, #f0f8ff 0%, #ffffff 100%) !important;
}

.ant-upload-drag .ant-upload-drag-icon {
  margin-bottom: 16px;
}

.ant-upload-drag .ant-upload-drag-icon .anticon {
  font-size: 48px;
  color: #1890ff;
}

.ant-upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 8px;
}

.ant-upload-hint {
  color: #8c8c8c;
  font-size: 14px;
}

/* Preview Modal Enhancements */
.ant-modal-content {
  border-radius: 12px;
  overflow: hidden;
}

.ant-modal-header {
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  border-bottom: 1px solid #f0f0f0;
  padding: 20px 24px;
}

.ant-modal-title {
  font-weight: 600;
  color: #262626;
}

.ant-descriptions-bordered .ant-descriptions-item-label {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  font-weight: 500;
  color: #595959;
}

.ant-descriptions-bordered .ant-descriptions-item-content {
  background: #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
  .attachment-controls {
    padding: 16px;
  }
  
  .attachment-controls .ant-row {
    gap: 12px;
  }
  
  .attachment-viewer .ant-table {
    font-size: 12px;
  }
  
  .attachment-viewer .ant-table-tbody > tr > td {
    padding: 12px 8px;
  }
  
  .attachment-controls .ant-btn {
    height: 36px;
    font-size: 12px;
  }
}

@media (max-width: 576px) {
  .attachment-controls {
    padding: 12px;
  }
  
  .attachment-viewer .ant-table-tbody > tr > td {
    padding: 8px 6px;
  }
  
  .attachment-viewer .anticon {
    font-size: 14px;
  }
  
  .attachment-viewer .ant-tag {
    font-size: 10px;
    padding: 1px 6px;
  }
}

/* Loading States */
.attachment-viewer .ant-spin-container {
  min-height: 200px;
}

.attachment-viewer .ant-table-placeholder {
  padding: 40px 20px;
}

.attachment-viewer .ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
}

/* Animation Enhancements */
.attachment-viewer .ant-table-tbody > tr {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Status Indicators */
.attachment-viewer .ant-tag.ant-tag-blue {
  background: linear-gradient(135deg, #e6f7ff 0%, #bae7ff 100%);
  color: #1890ff;
  border: 1px solid #91d5ff;
}

.attachment-viewer .ant-tag.ant-tag-orange {
  background: linear-gradient(135deg, #fff7e6 0%, #ffd591 100%);
  color: #fa8c16;
  border: 1px solid #ffb366;
}

.attachment-viewer .ant-tag.ant-tag-green {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  color: #52c41a;
  border: 1px solid #95de64;
}

.attachment-viewer .ant-tag.ant-tag-red {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  color: #ff4d4f;
  border: 1px solid #ff7875;
}

.attachment-viewer .ant-tag.ant-tag-purple {
  background: linear-gradient(135deg, #f9f0ff 0%, #d3adf7 100%);
  color: #722ed1;
  border: 1px solid #b37feb;
}

.attachment-viewer .ant-tag.ant-tag-gold {
  background: linear-gradient(135deg, #fffbe6 0%, #fff566 100%);
  color: #faad14;
  border: 1px solid #ffc53d;
}

/* Cosmic Enhancement */
.attachment-viewer .cosmic-enhancement {
  position: relative;
  overflow: hidden;
}

.attachment-viewer .cosmic-enhancement::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.attachment-viewer .cosmic-enhancement:hover::before {
  left: 100%;
}

/* Professional Spacing */
.attachment-viewer .ant-space {
  gap: 8px !important;
}

.attachment-viewer .ant-space-item {
  display: flex;
  align-items: center;
}

/* Enhanced Tooltips */
.ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 8px;
}

.ant-tooltip-arrow::before {
  background: rgba(0, 0, 0, 0.85);
}
