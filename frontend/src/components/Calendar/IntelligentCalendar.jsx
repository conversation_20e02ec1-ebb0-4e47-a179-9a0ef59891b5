/**
 * 🧠 INTELLIGENT CALENDAR - AI-POWERED HVAC SCHEDULING
 * 
 * Najinteligentniejszy kalendarz HVAC w Europie z AI features:
 * - Predictive scheduling
 * - Weather intelligence
 * - Smart conflict detection
 * - Customer preference learning
 * - Automatic optimization
 * - Voice commands dla szefa
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Button,
  Space,
  Typography,
  Alert,
  Badge,
  Tag,
  Tooltip,
  Modal,
  Select,
  DatePicker,
  TimePicker,
  Input,
  Form,
  Switch,
  Divider,
  notification,
  Spin,
  Progress,
  Avatar,
  List,
  Statistic
} from 'antd';
import {
  CalendarOutlined,
  RobotOutlined,
  ThunderboltOutlined,
  CloudOutlined,
  EnvironmentOutlined,
  ClockCircleOutlined,
  UserOutlined,
  ToolOutlined,
  BulbOutlined,
  SoundOutlined,
  ReloadOutlined,
  PlusOutlined,
  WarningOutlined,
  CheckCircleOutlined,
  StarOutlined,
  FireOutlined
} from '@ant-design/icons';
import FullCalendar from '@fullcalendar/react';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import moment from 'moment';
import { CosmicCard, CosmicSummaryCard } from '../cosmic';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;
const { TextArea } = Input;

const IntelligentCalendar = () => {
  const [loading, setLoading] = useState(false);
  const [aiProcessing, setAiProcessing] = useState(false);
  const [events, setEvents] = useState([]);
  const [selectedDate, setSelectedDate] = useState(moment().format('YYYY-MM-DD'));
  const [weatherData, setWeatherData] = useState(null);
  const [aiSuggestions, setAiSuggestions] = useState([]);
  const [voiceListening, setVoiceListening] = useState(false);
  const [smartModalVisible, setSmartModalVisible] = useState(false);
  const [optimizationResults, setOptimizationResults] = useState(null);
  
  // AI Intelligence States
  const [predictiveInsights, setPredictiveInsights] = useState({
    conflictPredictions: [],
    weatherAlerts: [],
    customerPreferences: [],
    routeOptimizations: [],
    maintenanceAlerts: []
  });

  /**
   * 🧠 AI-Powered Event Scheduling
   */
  const scheduleWithAI = useCallback(async (eventData) => {
    setAiProcessing(true);
    try {
      // Simulate AI processing
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      const aiOptimizedEvent = {
        ...eventData,
        id: Date.now(),
        aiOptimized: true,
        suggestedTime: moment(eventData.start).add(1, 'hour').format(),
        conflictScore: Math.random() * 0.3, // Low conflict score
        weatherScore: Math.random() * 0.2 + 0.8, // High weather score
        customerPreferenceScore: Math.random() * 0.3 + 0.7
      };

      setEvents(prev => [...prev, aiOptimizedEvent]);
      
      notification.success({
        message: '🧠 AI Scheduling Complete',
        description: 'Event scheduled with optimal timing and route optimization',
        duration: 5
      });

    } catch (error) {
      notification.error({
        message: '❌ AI Scheduling Error',
        description: 'Failed to optimize event scheduling'
      });
    } finally {
      setAiProcessing(false);
    }
  }, []);

  /**
   * 🌤️ Weather Intelligence Integration
   */
  const loadWeatherIntelligence = useCallback(async () => {
    try {
      // Mock weather data for Warsaw
      const mockWeatherData = {
        current: {
          temperature: 22,
          condition: 'sunny',
          humidity: 65,
          windSpeed: 12
        },
        forecast: [
          { date: moment().format('YYYY-MM-DD'), temp: 22, condition: 'sunny', hvacImpact: 'optimal' },
          { date: moment().add(1, 'day').format('YYYY-MM-DD'), temp: 18, condition: 'rainy', hvacImpact: 'indoor_only' },
          { date: moment().add(2, 'day').format('YYYY-MM-DD'), temp: 25, condition: 'hot', hvacImpact: 'high_demand' }
        ],
        alerts: [
          {
            type: 'weather_warning',
            message: 'Deszcz jutro - przełóż zewnętrzne instalacje',
            severity: 'medium',
            affectedServices: ['installation', 'outdoor_maintenance']
          }
        ]
      };

      setWeatherData(mockWeatherData);
      
      // Generate weather-based suggestions
      const weatherSuggestions = mockWeatherData.alerts.map(alert => ({
        type: 'weather',
        title: 'Weather Alert',
        description: alert.message,
        priority: alert.severity,
        action: 'reschedule_outdoor'
      }));

      setAiSuggestions(prev => [...prev, ...weatherSuggestions]);

    } catch (error) {
      console.error('Weather intelligence error:', error);
    }
  }, []);

  /**
   * 🎯 Smart Conflict Detection
   */
  const detectConflicts = useCallback((newEvent) => {
    const conflicts = events.filter(existingEvent => {
      const newStart = moment(newEvent.start);
      const newEnd = moment(newEvent.end);
      const existingStart = moment(existingEvent.start);
      const existingEnd = moment(existingEvent.end);

      return (
        (newStart.isBetween(existingStart, existingEnd) ||
         newEnd.isBetween(existingStart, existingEnd) ||
         existingStart.isBetween(newStart, newEnd)) &&
        existingEvent.technician === newEvent.technician
      );
    });

    if (conflicts.length > 0) {
      notification.warning({
        message: '⚠️ Conflict Detected',
        description: `Found ${conflicts.length} scheduling conflicts. AI will suggest alternatives.`,
        duration: 8
      });

      // Generate AI suggestions for conflict resolution
      const conflictSuggestions = conflicts.map(conflict => ({
        type: 'conflict',
        title: 'Scheduling Conflict',
        description: `Conflict with ${conflict.title} at ${moment(conflict.start).format('HH:mm')}`,
        priority: 'high',
        action: 'suggest_alternative',
        alternatives: [
          moment(newEvent.start).add(2, 'hours').format(),
          moment(newEvent.start).add(1, 'day').format(),
          moment(newEvent.start).subtract(1, 'hour').format()
        ]
      }));

      setAiSuggestions(prev => [...prev, ...conflictSuggestions]);
    }

    return conflicts;
  }, [events]);

  /**
   * 🗣️ Voice Commands for Boss
   */
  const startVoiceCommand = useCallback(() => {
    if (!('webkitSpeechRecognition' in window)) {
      notification.error({
        message: 'Voice Recognition Not Supported',
        description: 'Your browser does not support voice recognition'
      });
      return;
    }

    const recognition = new window.webkitSpeechRecognition();
    recognition.lang = 'pl-PL';
    recognition.continuous = false;
    recognition.interimResults = false;

    setVoiceListening(true);

    recognition.onresult = (event) => {
      const command = event.results[0][0].transcript.toLowerCase();
      processVoiceCommand(command);
    };

    recognition.onerror = () => {
      notification.error({
        message: 'Voice Recognition Error',
        description: 'Could not process voice command'
      });
      setVoiceListening(false);
    };

    recognition.onend = () => {
      setVoiceListening(false);
    };

    recognition.start();
  }, []);

  /**
   * 🎯 Process Voice Commands
   */
  const processVoiceCommand = useCallback((command) => {
    console.log('Voice command:', command);
    
    if (command.includes('pokaż') && command.includes('kalendarz')) {
      notification.success({
        message: '🎤 Voice Command Processed',
        description: 'Showing calendar view'
      });
    } else if (command.includes('optymalizuj') && command.includes('trasy')) {
      handleRouteOptimization();
    } else if (command.includes('nowe') && command.includes('wydarzenie')) {
      setSmartModalVisible(true);
    } else {
      notification.info({
        message: '🎤 Voice Command',
        description: `Processed: "${command}". Feature coming soon!`
      });
    }
  }, []);

  /**
   * 🚀 Route Optimization
   */
  const handleRouteOptimization = useCallback(async () => {
    setAiProcessing(true);
    try {
      // Simulate AI route optimization
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      const optimizationResult = {
        originalDistance: 145,
        optimizedDistance: 98,
        timeSaved: 47,
        fuelSaved: 12.5,
        optimizedRoute: [
          { order: 1, client: 'Jan Kowalski', address: 'Marszałkowska 123', time: '09:00' },
          { order: 2, client: 'Firma ABC', address: 'Biznesowa 45', time: '11:30' },
          { order: 3, client: 'Maria Nowak', address: 'Polna 67', time: '14:00' }
        ]
      };

      setOptimizationResults(optimizationResult);
      
      notification.success({
        message: '🚀 Route Optimization Complete',
        description: `Saved ${optimizationResult.timeSaved} minutes and ${optimizationResult.fuelSaved}L fuel`,
        duration: 8
      });

    } catch (error) {
      notification.error({
        message: '❌ Optimization Error',
        description: 'Failed to optimize routes'
      });
    } finally {
      setAiProcessing(false);
    }
  }, []);

  // Load initial data
  useEffect(() => {
    loadWeatherIntelligence();
    
    // Generate some demo events
    const demoEvents = [
      {
        id: 1,
        title: 'Serwis klimatyzacji - Jan Kowalski',
        start: moment().hour(9).minute(0).format(),
        end: moment().hour(11).minute(0).format(),
        backgroundColor: '#52c41a',
        category: 'service',
        technician: 'tech1',
        client: 'Jan Kowalski',
        priority: 'medium'
      },
      {
        id: 2,
        title: 'Instalacja pompy ciepła - Firma ABC',
        start: moment().add(1, 'day').hour(14).minute(0).format(),
        end: moment().add(1, 'day').hour(17).minute(0).format(),
        backgroundColor: '#1890ff',
        category: 'installation',
        technician: 'tech2',
        client: 'Firma ABC',
        priority: 'high'
      }
    ];

    setEvents(demoEvents);
  }, [loadWeatherIntelligence]);

  return (
    <div style={{ padding: 'var(--space-lg)' }} className="animate-fade-in">
      {/* Header */}
      <div style={{ marginBottom: 'var(--space-lg)' }}>
        <Title level={2} style={{ color: 'var(--hvac-primary)' }}>
          🧠 Intelligent Calendar - AI HVAC Scheduling
        </Title>
        <Text type="secondary" style={{ fontSize: 'var(--font-size-md)' }}>
          Najinteligentniejszy kalendarz HVAC w Europie z AI features
        </Text>
      </div>

      {/* AI Control Panel */}
      <Row gutter={[16, 16]} style={{ marginBottom: 'var(--space-lg)' }}>
        <Col xs={24} lg={8}>
          <CosmicCard 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <RobotOutlined style={{ color: 'var(--hvac-primary)' }} />
                🧠 AI Assistant
              </div>
            }
            hoverable
          >
            <Space direction="vertical" style={{ width: '100%' }}>
              <Button 
                type="primary" 
                icon={voiceListening ? <SoundOutlined /> : <RobotOutlined />}
                onClick={startVoiceCommand}
                loading={voiceListening}
                style={{ width: '100%' }}
                className="cosmic-btn-primary"
              >
                {voiceListening ? '🎤 Listening...' : '🎤 Voice Command'}
              </Button>
              
              <Button 
                icon={<ThunderboltOutlined />}
                onClick={handleRouteOptimization}
                loading={aiProcessing}
                style={{ width: '100%' }}
                className="cosmic-btn-secondary"
              >
                🚀 Optimize Routes
              </Button>
              
              <Button 
                icon={<PlusOutlined />}
                onClick={() => setSmartModalVisible(true)}
                style={{ width: '100%' }}
                className="cosmic-btn"
              >
                ➕ Smart Schedule
              </Button>
            </Space>
          </CosmicCard>
        </Col>

        <Col xs={24} lg={8}>
          <CosmicCard 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <CloudOutlined style={{ color: 'var(--hvac-warning)' }} />
                🌤️ Weather Intelligence
              </div>
            }
            hoverable
          >
            {weatherData ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Text strong>{weatherData.current.temperature}°C</Text>
                  <Tag color="blue">{weatherData.current.condition}</Tag>
                </div>
                <Progress 
                  percent={weatherData.current.humidity} 
                  format={percent => `${percent}% humidity`}
                  strokeColor="var(--hvac-primary)"
                />
                {weatherData.alerts.map((alert, index) => (
                  <Alert
                    key={index}
                    message={alert.message}
                    type="warning"
                    size="small"
                    showIcon
                  />
                ))}
              </Space>
            ) : (
              <Spin />
            )}
          </CosmicCard>
        </Col>

        <Col xs={24} lg={8}>
          <CosmicCard 
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <BulbOutlined style={{ color: 'var(--hvac-success)' }} />
                💡 AI Suggestions
              </div>
            }
            hoverable
          >
            <List
              size="small"
              dataSource={aiSuggestions.slice(0, 3)}
              renderItem={(suggestion, index) => (
                <List.Item style={{ padding: 'var(--space-xs) 0' }}>
                  <div style={{ width: '100%' }}>
                    <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-xs)' }}>
                      <Badge 
                        status={suggestion.priority === 'high' ? 'error' : 'processing'} 
                      />
                      <Text strong style={{ fontSize: 'var(--font-size-sm)' }}>
                        {suggestion.title}
                      </Text>
                    </div>
                    <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                      {suggestion.description}
                    </Text>
                  </div>
                </List.Item>
              )}
            />
            {aiSuggestions.length === 0 && (
              <Text type="secondary">No AI suggestions at the moment</Text>
            )}
          </CosmicCard>
        </Col>
      </Row>

      {/* Optimization Results */}
      {optimizationResults && (
        <Row gutter={[16, 16]} style={{ marginBottom: 'var(--space-lg)' }}>
          <Col xs={24}>
            <CosmicCard
              title="🚀 Route Optimization Results"
              status="success"
              className="animate-scale-in"
            >
              <Row gutter={[16, 16]}>
                <Col xs={24} sm={6}>
                  <CosmicSummaryCard>
                    <Statistic
                      title="Distance Saved"
                      value={optimizationResults.originalDistance - optimizationResults.optimizedDistance}
                      suffix="km"
                      prefix={<EnvironmentOutlined style={{ color: 'var(--hvac-success)' }} />}
                      valueStyle={{ color: 'var(--hvac-success)' }}
                    />
                  </CosmicSummaryCard>
                </Col>
                <Col xs={24} sm={6}>
                  <CosmicSummaryCard>
                    <Statistic
                      title="Time Saved"
                      value={optimizationResults.timeSaved}
                      suffix="min"
                      prefix={<ClockCircleOutlined style={{ color: 'var(--hvac-primary)' }} />}
                      valueStyle={{ color: 'var(--hvac-primary)' }}
                    />
                  </CosmicSummaryCard>
                </Col>
                <Col xs={24} sm={6}>
                  <CosmicSummaryCard>
                    <Statistic
                      title="Fuel Saved"
                      value={optimizationResults.fuelSaved}
                      suffix="L"
                      prefix={<FireOutlined style={{ color: 'var(--hvac-warning)' }} />}
                      valueStyle={{ color: 'var(--hvac-warning)' }}
                    />
                  </CosmicSummaryCard>
                </Col>
                <Col xs={24} sm={6}>
                  <CosmicSummaryCard>
                    <Statistic
                      title="Efficiency"
                      value={Math.round((optimizationResults.timeSaved / 60) * 100)}
                      suffix="%"
                      prefix={<StarOutlined style={{ color: 'var(--hvac-secondary)' }} />}
                      valueStyle={{ color: 'var(--hvac-secondary)' }}
                    />
                  </CosmicSummaryCard>
                </Col>
              </Row>
            </CosmicCard>
          </Col>
        </Row>
      )}

      {/* Main Calendar */}
      <Row gutter={[16, 16]}>
        <Col xs={24}>
          <CosmicCard
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <CalendarOutlined style={{ color: 'var(--hvac-primary)' }} />
                📅 AI-Powered HVAC Calendar
              </div>
            }
            extra={
              <Space>
                <Badge status="processing" text="AI Active" />
                <Button
                  icon={<ReloadOutlined />}
                  onClick={() => window.location.reload()}
                  className="cosmic-btn"
                >
                  Refresh
                </Button>
              </Space>
            }
            hoverable
          >
            <FullCalendar
              plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
              headerToolbar={{
                left: 'prev,next today',
                center: 'title',
                right: 'dayGridMonth,timeGridWeek,timeGridDay'
              }}
              initialView="timeGridWeek"
              events={events}
              height="600px"
              locale="pl"
              firstDay={1}
              slotMinTime="07:00:00"
              slotMaxTime="19:00:00"
              businessHours={{
                daysOfWeek: [1, 2, 3, 4, 5],
                startTime: '08:00',
                endTime: '17:00'
              }}
              eventClick={(info) => {
                const event = info.event;
                Modal.info({
                  title: `📋 ${event.title}`,
                  content: (
                    <div>
                      <Space direction="vertical" style={{ width: '100%' }}>
                        <div>
                          <Text strong>🕐 Time: </Text>
                          <Text>{moment(event.start).format('DD/MM/YYYY HH:mm')} - {moment(event.end).format('HH:mm')}</Text>
                        </div>
                        <div>
                          <Text strong>👤 Client: </Text>
                          <Text>{event.extendedProps.client}</Text>
                        </div>
                        <div>
                          <Text strong>🔧 Category: </Text>
                          <Tag color="blue">{event.extendedProps.category}</Tag>
                        </div>
                        <div>
                          <Text strong>⚡ Priority: </Text>
                          <Tag color={event.extendedProps.priority === 'high' ? 'red' : 'orange'}>
                            {event.extendedProps.priority}
                          </Tag>
                        </div>
                        {event.extendedProps.aiOptimized && (
                          <Alert
                            message="🧠 AI Optimized Event"
                            description="This event was scheduled using AI optimization"
                            type="info"
                            showIcon
                          />
                        )}
                      </Space>
                    </div>
                  ),
                  width: 500
                });
              }}
              dateClick={(info) => {
                setSelectedDate(info.dateStr);
                setSmartModalVisible(true);
              }}
              eventDrop={(info) => {
                // Handle event drag and drop with conflict detection
                const conflicts = detectConflicts({
                  start: info.event.start,
                  end: info.event.end,
                  technician: info.event.extendedProps.technician
                });

                if (conflicts.length > 0) {
                  info.revert();
                }
              }}
            />
          </CosmicCard>
        </Col>
      </Row>

      {/* Smart Scheduling Modal */}
      <Modal
        title={
          <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
            <RobotOutlined style={{ color: 'var(--hvac-primary)' }} />
            🧠 AI Smart Scheduling
          </div>
        }
        open={smartModalVisible}
        onCancel={() => setSmartModalVisible(false)}
        footer={null}
        width={700}
        className="cosmic-modal"
      >
        <Form
          layout="vertical"
          onFinish={(values) => {
            const eventData = {
              ...values,
              start: moment(selectedDate).hour(values.time.hour()).minute(values.time.minute()).format(),
              end: moment(selectedDate).hour(values.time.hour() + 2).minute(values.time.minute()).format()
            };
            scheduleWithAI(eventData);
            setSmartModalVisible(false);
          }}
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12}>
              <Form.Item
                label="📋 Event Title"
                name="title"
                rules={[{ required: true, message: 'Please enter event title' }]}
              >
                <Input placeholder="e.g., Serwis klimatyzacji" />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="🕐 Preferred Time"
                name="time"
                rules={[{ required: true, message: 'Please select time' }]}
              >
                <TimePicker format="HH:mm" style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="👤 Customer"
                name="client"
                rules={[{ required: true, message: 'Please select customer' }]}
              >
                <Select placeholder="Select customer">
                  <Option value="Jan Kowalski">Jan Kowalski</Option>
                  <Option value="Firma ABC">Firma ABC Sp. z o.o.</Option>
                  <Option value="Maria Nowak">Maria Nowak</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24} sm={12}>
              <Form.Item
                label="🔧 Service Type"
                name="category"
                rules={[{ required: true, message: 'Please select service type' }]}
              >
                <Select placeholder="Select service type">
                  <Option value="service">🔧 Serwis</Option>
                  <Option value="installation">🏗️ Instalacja</Option>
                  <Option value="inspection">🔍 Oględziny</Option>
                </Select>
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item
                label="📝 Description"
                name="description"
              >
                <TextArea rows={3} placeholder="Additional details..." />
              </Form.Item>
            </Col>
            <Col xs={24}>
              <Form.Item>
                <Space>
                  <Switch defaultChecked />
                  <Text>🧠 Use AI Optimization</Text>
                </Space>
              </Form.Item>
            </Col>
          </Row>

          <Divider />

          <div style={{ textAlign: 'right' }}>
            <Space>
              <Button onClick={() => setSmartModalVisible(false)}>
                Cancel
              </Button>
              <Button
                type="primary"
                htmlType="submit"
                loading={aiProcessing}
                className="cosmic-btn-primary"
              >
                🧠 Schedule with AI
              </Button>
            </Space>
          </div>
        </Form>
      </Modal>
    </div>
  );
};

export default IntelligentCalendar;
