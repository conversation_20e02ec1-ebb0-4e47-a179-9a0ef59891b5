/**
 * 🎯 Enhanced Customer 360° Profile Styles
 * Professional styling for comprehensive customer intelligence
 */

.enhanced-360-profile {
  padding: 0;
  background: transparent;
}

.profile-header {
  margin-bottom: 24px;
  padding: 24px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 12px;
  border: 1px solid #f0f0f0;
}

.profile-header .ant-typography h2 {
  margin-bottom: 4px;
  color: #262626;
  font-weight: 600;
}

.profile-tabs {
  background: #ffffff;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.profile-tabs .ant-tabs-nav {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  margin: 0;
  padding: 0 24px;
}

.profile-tabs .ant-tabs-tab {
  border-radius: 8px 8px 0 0;
  border: none;
  background: transparent;
  margin-right: 8px;
  padding: 12px 16px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.profile-tabs .ant-tabs-tab:hover {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.profile-tabs .ant-tabs-tab-active {
  background: #ffffff;
  color: #1890ff;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.profile-tabs .ant-tabs-content-holder {
  padding: 24px;
}

/* Customer Info Card */
.customer-info-card .customer-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 16px;
}

.customer-info-card .customer-details h4 {
  margin-bottom: 4px;
  color: #262626;
}

.customer-info-card .ant-descriptions-item-label {
  font-weight: 500;
  color: #595959;
}

/* Timeline Enhancements */
.timeline-item {
  padding: 12px 0;
}

.timeline-header {
  margin-bottom: 8px;
}

.timeline-content {
  margin-left: 0;
}

.timeline-content .ant-typography {
  margin-bottom: 8px;
}

.timeline-event {
  padding: 8px 0;
}

.event-header {
  margin-bottom: 8px;
}

.event-content {
  margin-left: 0;
}

/* Equipment Cards */
.equipment-card {
  border-radius: 8px;
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.equipment-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.equipment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

/* Financial Tab */
.financial-tab .ant-statistic {
  text-align: center;
  padding: 16px;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

.payment-stats {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 12px;
  background: linear-gradient(135deg, #fafafa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
}

/* Insight Items */
.insight-item {
  padding: 16px;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border-radius: 8px;
  border: 1px solid #f0f0f0;
  transition: all 0.3s ease;
}

.insight-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* Progress Enhancements */
.enhanced-360-profile .ant-progress-line {
  margin-bottom: 8px;
}

.enhanced-360-profile .ant-progress-text {
  font-size: 12px;
  font-weight: 500;
}

/* Tag Enhancements */
.enhanced-360-profile .ant-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 11px;
  padding: 2px 8px;
  border: none;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* List Enhancements */
.enhanced-360-profile .ant-list-item {
  padding: 12px 0;
  border-bottom: 1px solid #f5f5f5;
  transition: all 0.2s ease;
}

.enhanced-360-profile .ant-list-item:hover {
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  padding-left: 12px;
  padding-right: 12px;
  border-radius: 6px;
  margin: 0 -12px;
}

/* Statistic Enhancements */
.enhanced-360-profile .ant-statistic-title {
  font-size: 12px;
  font-weight: 500;
  color: #8c8c8c;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 8px;
}

.enhanced-360-profile .ant-statistic-content {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.2;
}

/* Descriptions Enhancements */
.enhanced-360-profile .ant-descriptions-item-label {
  background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
  font-weight: 500;
  color: #595959;
  padding: 8px 12px;
}

.enhanced-360-profile .ant-descriptions-item-content {
  background: #ffffff;
  padding: 8px 12px;
}

/* Empty States */
.enhanced-360-profile .ant-empty {
  padding: 40px 20px;
}

.enhanced-360-profile .ant-empty-description {
  color: #8c8c8c;
  font-size: 14px;
  margin-top: 16px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-header {
    padding: 16px;
  }
  
  .profile-tabs .ant-tabs-content-holder {
    padding: 16px;
  }
  
  .customer-info-card .customer-header {
    flex-direction: column;
    text-align: center;
    gap: 12px;
  }
  
  .timeline-item {
    padding: 8px 0;
  }
  
  .equipment-card {
    margin-bottom: 12px;
  }
  
  .stat-item {
    padding: 8px;
  }
  
  .insight-item {
    padding: 12px;
  }
}

@media (max-width: 576px) {
  .profile-header {
    padding: 12px;
  }
  
  .profile-tabs .ant-tabs-content-holder {
    padding: 12px;
  }
  
  .profile-tabs .ant-tabs-nav {
    padding: 0 12px;
  }
  
  .profile-tabs .ant-tabs-tab {
    padding: 8px 12px;
    font-size: 12px;
  }
  
  .enhanced-360-profile .ant-statistic-content {
    font-size: 20px;
  }
  
  .enhanced-360-profile .ant-tag {
    font-size: 10px;
    padding: 1px 6px;
  }
}

/* Animation Enhancements */
.enhanced-360-profile .ant-card {
  animation: fadeInUp 0.3s ease;
}

.enhanced-360-profile .ant-timeline-item {
  animation: fadeInLeft 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Loading States */
.enhanced-360-profile .ant-spin-container {
  min-height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Color Variations */
.enhanced-360-profile .health-score-excellent {
  color: #52c41a;
}

.enhanced-360-profile .health-score-good {
  color: #1890ff;
}

.enhanced-360-profile .health-score-warning {
  color: #fa8c16;
}

.enhanced-360-profile .health-score-danger {
  color: #ff4d4f;
}

/* Cosmic Enhancement */
.enhanced-360-profile .cosmic-enhancement {
  position: relative;
  overflow: hidden;
}

.enhanced-360-profile .cosmic-enhancement::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s ease;
}

.enhanced-360-profile .cosmic-enhancement:hover::before {
  left: 100%;
}

/* Professional Spacing */
.enhanced-360-profile .ant-space {
  gap: 8px !important;
}

.enhanced-360-profile .ant-space-item {
  display: flex;
  align-items: center;
}

/* Enhanced Tooltips */
.enhanced-360-profile .ant-tooltip-inner {
  background: rgba(0, 0, 0, 0.85);
  border-radius: 6px;
  font-size: 12px;
  padding: 6px 8px;
}

.enhanced-360-profile .ant-tooltip-arrow::before {
  background: rgba(0, 0, 0, 0.85);
}
