/**
 * 🎯 Enhanced Customer 360° Profile - Complete Customer Intelligence
 * Professional customer profile with all data sources unified
 */

import React, { useState, useEffect } from 'react';
import {
  Card,
  Tabs,
  Row,
  Col,
  Statistic,
  Progress,
  Timeline,
  Tag,
  Avatar,
  Typography,
  Divider,
  Space,
  Button,
  Badge,
  Tooltip,
  Spin,
  Alert,
  List,
  Descriptions
} from 'antd';
import {
  UserOutlined,
  MailOutlined,
  PhoneOutlined,
  HomeOutlined,
  DollarOutlined,
  CalendarOutlined,
  ToolOutlined,
  FileTextOutlined,
  SoundOutlined,
  RobotOutlined,
  TrophyOutlined,
  WarningOutlined,
  HeartOutlined,
  ThunderboltOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined
} from '@ant-design/icons';
import { CosmicCard, CosmicSummaryCard } from '../cosmic';
import AttachmentViewer from '../AttachmentViewer';
import './Enhanced360Profile.css';

const { Title, Text, Paragraph } = Typography;
const { TabPane } = Tabs;

const Enhanced360Profile = ({ customerId, customer = null }) => {
  const [loading, setLoading] = useState(false);
  const [customerData, setCustomerData] = useState(customer);
  const [profile360, setProfile360] = useState(null);
  const [activeTab, setActiveTab] = useState('overview');

  useEffect(() => {
    if (customerId) {
      fetchCustomer360Profile();
    }
  }, [customerId]);

  const fetchCustomer360Profile = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/customer-interactions/customer/${customerId}/360-profile`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      const data = await response.json();

      if (data.success) {
        setProfile360(data.data);
        if (data.data.customer) {
          setCustomerData(data.data.customer);
        }
      }
    } catch (error) {
      console.error('Error fetching 360° profile:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading && !profile360) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <div style={{ marginTop: 16 }}>Loading customer profile...</div>
      </div>
    );
  }

  if (!customerData && !profile360) {
    return (
      <Alert
        message="Customer Not Found"
        description="Unable to load customer profile data."
        type="warning"
        showIcon
      />
    );
  }

  const renderOverviewTab = () => (
    <div className="profile-overview">
      <Row gutter={[24, 24]}>
        {/* Customer Basic Info */}
        <Col xs={24} lg={8}>
          <CosmicCard title="👤 Customer Information" className="customer-info-card">
            <div className="customer-header">
              <Avatar size={64} icon={<UserOutlined />} />
              <div className="customer-details">
                <Title level={4}>{customerData?.name || 'Unknown Customer'}</Title>
                <Text type="secondary">{customerData?.email}</Text>
              </div>
            </div>
            
            <Divider />
            
            <Descriptions column={1} size="small">
              <Descriptions.Item label="Phone">
                <Space>
                  <PhoneOutlined />
                  {customerData?.phone || 'Not provided'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="Address">
                <Space>
                  <HomeOutlined />
                  {customerData?.address || 'Not provided'}
                </Space>
              </Descriptions.Item>
              <Descriptions.Item label="Company">
                {customerData?.company || 'Individual'}
              </Descriptions.Item>
              <Descriptions.Item label="Customer Since">
                <Space>
                  <CalendarOutlined />
                  {customerData?.createdAt ? new Date(customerData.createdAt).toLocaleDateString() : 'Unknown'}
                </Space>
              </Descriptions.Item>
            </Descriptions>
          </CosmicCard>
        </Col>

        {/* Health Score & Analytics */}
        <Col xs={24} lg={16}>
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={12} md={6}>
              <CosmicSummaryCard
                title="Health Score"
                value={profile360?.analytics?.healthScore || 0}
                suffix="/100"
                icon={<HeartOutlined />}
                color={profile360?.analytics?.healthScore > 70 ? "#52c41a" : profile360?.analytics?.healthScore > 40 ? "#fa8c16" : "#ff4d4f"}
                trend={profile360?.analytics?.healthTrend}
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <CosmicSummaryCard
                title="Total Interactions"
                value={profile360?.summary?.totalInteractions || 0}
                icon={<MailOutlined />}
                color="#1890ff"
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <CosmicSummaryCard
                title="Service Orders"
                value={profile360?.summary?.serviceOrders || 0}
                icon={<ToolOutlined />}
                color="#722ed1"
              />
            </Col>
            <Col xs={24} sm={12} md={6}>
              <CosmicSummaryCard
                title="Total Value"
                value={`$${profile360?.summary?.totalValue || 0}`}
                icon={<DollarOutlined />}
                color="#52c41a"
              />
            </Col>
          </Row>

          {/* AI Insights */}
          {profile360?.aiInsights && (
            <CosmicCard title="🤖 AI Insights" style={{ marginTop: 16 }}>
              <Row gutter={[16, 16]}>
                <Col xs={24} md={12}>
                  <div className="insight-item">
                    <Space>
                      <RobotOutlined style={{ color: '#1890ff' }} />
                      <div>
                        <Text strong>Churn Risk</Text>
                        <div>
                          <Progress
                            percent={profile360.aiInsights.churnProbability * 100}
                            size="small"
                            status={profile360.aiInsights.churnProbability > 0.7 ? 'exception' : 'normal'}
                          />
                        </div>
                      </div>
                    </Space>
                  </div>
                </Col>
                <Col xs={24} md={12}>
                  <div className="insight-item">
                    <Space>
                      <TrophyOutlined style={{ color: '#faad14' }} />
                      <div>
                        <Text strong>Lifetime Value</Text>
                        <div>
                          <Text type="secondary">${profile360.aiInsights.lifetimeValue || 0}</Text>
                        </div>
                      </div>
                    </Space>
                  </div>
                </Col>
              </Row>
              
              {profile360.aiInsights.recommendations && (
                <div style={{ marginTop: 16 }}>
                  <Text strong>Recommendations:</Text>
                  <List
                    size="small"
                    dataSource={profile360.aiInsights.recommendations}
                    renderItem={(item, index) => (
                      <List.Item>
                        <Space>
                          <Badge count={index + 1} style={{ backgroundColor: '#1890ff' }} />
                          <Text>{item}</Text>
                        </Space>
                      </List.Item>
                    )}
                  />
                </div>
              )}
            </CosmicCard>
          )}
        </Col>
      </Row>
    </div>
  );

  const renderCommunicationsTab = () => (
    <div className="communications-tab">
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <CosmicCard title="📧 Communication Timeline">
            {profile360?.communications?.length > 0 ? (
              <Timeline mode="left">
                {profile360.communications.map((comm, index) => (
                  <Timeline.Item
                    key={index}
                    dot={
                      comm.type === 'email' ? <MailOutlined /> :
                      comm.type === 'transcription' ? <SoundOutlined /> :
                      <FileTextOutlined />
                    }
                    color={
                      comm.sentiment === 'positive' ? 'green' :
                      comm.sentiment === 'negative' ? 'red' : 'blue'
                    }
                  >
                    <div className="timeline-item">
                      <div className="timeline-header">
                        <Space>
                          <Tag color={comm.type === 'email' ? 'blue' : 'purple'}>
                            {comm.type.toUpperCase()}
                          </Tag>
                          <Text type="secondary">
                            {new Date(comm.date).toLocaleString()}
                          </Text>
                          {comm.sentiment && (
                            <Tag color={
                              comm.sentiment === 'positive' ? 'green' :
                              comm.sentiment === 'negative' ? 'red' : 'default'
                            }>
                              {comm.sentiment}
                            </Tag>
                          )}
                        </Space>
                      </div>
                      <div className="timeline-content">
                        <Text strong>{comm.subject || comm.title}</Text>
                        <Paragraph ellipsis={{ rows: 2, expandable: true }}>
                          {comm.content || comm.summary}
                        </Paragraph>
                      </div>
                    </div>
                  </Timeline.Item>
                ))}
              </Timeline>
            ) : (
              <div style={{ textAlign: 'center', padding: '40px' }}>
                <MailOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
                <div style={{ marginTop: 16 }}>
                  <Text type="secondary">No communications found</Text>
                </div>
              </div>
            )}
          </CosmicCard>
        </Col>
        
        <Col xs={24} lg={8}>
          <CosmicCard title="📊 Communication Stats">
            <Row gutter={[16, 16]}>
              <Col span={24}>
                <Statistic
                  title="Total Emails"
                  value={profile360?.summary?.emailCount || 0}
                  prefix={<MailOutlined />}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="Transcriptions"
                  value={profile360?.summary?.transcriptionCount || 0}
                  prefix={<SoundOutlined />}
                />
              </Col>
              <Col span={24}>
                <Statistic
                  title="Avg Response Time"
                  value={profile360?.analytics?.avgResponseTime || 0}
                  suffix="hours"
                  prefix={<ClockCircleOutlined />}
                />
              </Col>
            </Row>
            
            {profile360?.analytics?.sentimentDistribution && (
              <div style={{ marginTop: 24 }}>
                <Text strong>Sentiment Distribution</Text>
                <div style={{ marginTop: 8 }}>
                  <Progress
                    percent={profile360.analytics.sentimentDistribution.positive}
                    strokeColor="#52c41a"
                    format={() => `${profile360.analytics.sentimentDistribution.positive}% Positive`}
                  />
                  <Progress
                    percent={profile360.analytics.sentimentDistribution.neutral}
                    strokeColor="#1890ff"
                    format={() => `${profile360.analytics.sentimentDistribution.neutral}% Neutral`}
                  />
                  <Progress
                    percent={profile360.analytics.sentimentDistribution.negative}
                    strokeColor="#ff4d4f"
                    format={() => `${profile360.analytics.sentimentDistribution.negative}% Negative`}
                  />
                </div>
              </div>
            )}
          </CosmicCard>
        </Col>
      </Row>
    </div>
  );

  const renderEquipmentTab = () => (
    <div className="equipment-tab">
      <CosmicCard title="🔧 Equipment & Service History">
        {profile360?.equipment?.length > 0 ? (
          <Row gutter={[16, 16]}>
            {profile360.equipment.map((equipment, index) => (
              <Col xs={24} md={12} lg={8} key={index}>
                <Card size="small" className="equipment-card">
                  <div className="equipment-header">
                    <Space>
                      <ToolOutlined style={{ color: '#1890ff' }} />
                      <Text strong>{equipment.name || equipment.type}</Text>
                    </Space>
                    <Tag color={equipment.status === 'active' ? 'green' : 'orange'}>
                      {equipment.status}
                    </Tag>
                  </div>
                  <div style={{ marginTop: 12 }}>
                    <Text type="secondary">Health Score</Text>
                    <Progress
                      percent={equipment.healthScore || 0}
                      size="small"
                      status={equipment.healthScore > 70 ? 'normal' : 'exception'}
                    />
                  </div>
                  <div style={{ marginTop: 8 }}>
                    <Text type="secondary">
                      Last Service: {equipment.lastService ? 
                        new Date(equipment.lastService).toLocaleDateString() : 
                        'Never'
                      }
                    </Text>
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <ToolOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">No equipment registered</Text>
            </div>
          </div>
        )}
      </CosmicCard>
    </div>
  );

  const renderFinancialTab = () => (
    <div className="financial-tab">
      <Row gutter={[24, 24]}>
        <Col xs={24} lg={16}>
          <CosmicCard title="💰 Financial Overview">
            <Row gutter={[16, 16]}>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Total Revenue"
                  value={profile360?.financial?.totalRevenue || 0}
                  prefix="$"
                  valueStyle={{ color: '#52c41a' }}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Outstanding"
                  value={profile360?.financial?.outstanding || 0}
                  prefix="$"
                  valueStyle={{ color: '#fa8c16' }}
                />
              </Col>
              <Col xs={24} sm={8}>
                <Statistic
                  title="Avg Order Value"
                  value={profile360?.financial?.avgOrderValue || 0}
                  prefix="$"
                  valueStyle={{ color: '#1890ff' }}
                />
              </Col>
            </Row>
            
            {profile360?.invoices?.length > 0 && (
              <div style={{ marginTop: 24 }}>
                <Title level={5}>Recent Invoices</Title>
                <List
                  size="small"
                  dataSource={profile360.invoices.slice(0, 5)}
                  renderItem={(invoice) => (
                    <List.Item>
                      <List.Item.Meta
                        title={`Invoice #${invoice.number}`}
                        description={`${new Date(invoice.date).toLocaleDateString()} - $${invoice.amount}`}
                      />
                      <Tag color={invoice.status === 'paid' ? 'green' : 'orange'}>
                        {invoice.status}
                      </Tag>
                    </List.Item>
                  )}
                />
              </div>
            )}
          </CosmicCard>
        </Col>
        
        <Col xs={24} lg={8}>
          <CosmicCard title="📈 Payment Behavior">
            <div className="payment-stats">
              <div className="stat-item">
                <Text type="secondary">Payment Score</Text>
                <Progress
                  percent={profile360?.analytics?.paymentScore || 0}
                  strokeColor="#52c41a"
                />
              </div>
              <div className="stat-item">
                <Text type="secondary">Avg Payment Time</Text>
                <Text strong>{profile360?.analytics?.avgPaymentTime || 0} days</Text>
              </div>
              <div className="stat-item">
                <Text type="secondary">Late Payments</Text>
                <Text strong>{profile360?.analytics?.latePayments || 0}</Text>
              </div>
            </div>
          </CosmicCard>
        </Col>
      </Row>
    </div>
  );

  const renderAttachmentsTab = () => (
    <div className="attachments-tab">
      <AttachmentViewer customerId={customerId} embedded={true} />
    </div>
  );

  const renderTimelineTab = () => (
    <div className="timeline-tab">
      <CosmicCard title="📅 Complete Customer Timeline">
        {profile360?.timeline?.length > 0 ? (
          <Timeline mode="left">
            {profile360.timeline.map((event, index) => (
              <Timeline.Item
                key={index}
                dot={
                  event.type === 'email' ? <MailOutlined /> :
                  event.type === 'service' ? <ToolOutlined /> :
                  event.type === 'invoice' ? <DollarOutlined /> :
                  event.type === 'attachment' ? <FileTextOutlined /> :
                  <CheckCircleOutlined />
                }
                color={
                  event.type === 'email' ? 'blue' :
                  event.type === 'service' ? 'purple' :
                  event.type === 'invoice' ? 'green' :
                  event.type === 'attachment' ? 'orange' : 'gray'
                }
              >
                <div className="timeline-event">
                  <div className="event-header">
                    <Space>
                      <Tag color={
                        event.type === 'email' ? 'blue' :
                        event.type === 'service' ? 'purple' :
                        event.type === 'invoice' ? 'green' :
                        event.type === 'attachment' ? 'orange' : 'default'
                      }>
                        {event.type.toUpperCase()}
                      </Tag>
                      <Text type="secondary">
                        {new Date(event.date).toLocaleString()}
                      </Text>
                    </Space>
                  </div>
                  <div className="event-content">
                    <Text strong>{event.title}</Text>
                    {event.description && (
                      <div>
                        <Text type="secondary">{event.description}</Text>
                      </div>
                    )}
                  </div>
                </div>
              </Timeline.Item>
            ))}
          </Timeline>
        ) : (
          <div style={{ textAlign: 'center', padding: '40px' }}>
            <CalendarOutlined style={{ fontSize: '48px', color: '#d9d9d9' }} />
            <div style={{ marginTop: 16 }}>
              <Text type="secondary">No timeline events found</Text>
            </div>
          </div>
        )}
      </CosmicCard>
    </div>
  );

  return (
    <div className="enhanced-360-profile">
      <div className="profile-header">
        <Row justify="space-between" align="middle">
          <Col>
            <Title level={2}>
              🎯 Customer 360° Profile
            </Title>
            <Text type="secondary">
              Complete customer intelligence and interaction history
            </Text>
          </Col>
          <Col>
            <Space>
              <Button
                icon={<ThunderboltOutlined />}
                onClick={fetchCustomer360Profile}
                loading={loading}
              >
                Refresh
              </Button>
            </Space>
          </Col>
        </Row>
      </div>

      <Tabs
        activeKey={activeTab}
        onChange={setActiveTab}
        type="card"
        className="profile-tabs"
      >
        <TabPane tab="📊 Overview" key="overview">
          {renderOverviewTab()}
        </TabPane>
        <TabPane tab="💬 Communications" key="communications">
          {renderCommunicationsTab()}
        </TabPane>
        <TabPane tab="🔧 Equipment" key="equipment">
          {renderEquipmentTab()}
        </TabPane>
        <TabPane tab="💰 Financial" key="financial">
          {renderFinancialTab()}
        </TabPane>
        <TabPane tab="📎 Attachments" key="attachments">
          {renderAttachmentsTab()}
        </TabPane>
        <TabPane tab="📅 Timeline" key="timeline">
          {renderTimelineTab()}
        </TabPane>
      </Tabs>
    </div>
  );
};

export default Enhanced360Profile;
