import React, { createContext, useContext, useEffect, useState } from 'react';
import { ConfigProvider, theme } from 'antd';

const ThemeContext = createContext();

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (!context) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

const CosmicThemeProvider = ({ children }) => {
  const [isDarkMode, setIsDarkMode] = useState(() => {
    // Check localStorage first, then system preference
    const saved = localStorage.getItem('hvac-theme');
    if (saved) {
      return saved === 'dark';
    }
    return window.matchMedia('(prefers-color-scheme: dark)').matches;
  });

  useEffect(() => {
    // Apply theme to document
    document.documentElement.setAttribute('data-theme', isDarkMode ? 'dark' : 'light');
    localStorage.setItem('hvac-theme', isDarkMode ? 'dark' : 'light');
  }, [isDarkMode]);

  const toggleTheme = () => {
    setIsDarkMode(prev => !prev);
  };

  // HVAC-specific Ant Design theme configuration
  const antdTheme = {
    algorithm: isDarkMode ? theme.darkAlgorithm : theme.defaultAlgorithm,
    token: {
      // Primary colors
      colorPrimary: '#1890ff',
      colorSuccess: '#52c41a',
      colorWarning: '#faad14',
      colorError: '#ff4d4f',
      colorInfo: '#13c2c2',
      
      // Typography
      fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif',
      fontSize: 14,
      fontSizeHeading1: 32,
      fontSizeHeading2: 24,
      fontSizeHeading3: 18,
      fontSizeHeading4: 16,
      fontSizeHeading5: 14,
      
      // Spacing (Golden Ratio based)
      padding: 13,
      paddingXS: 4,
      paddingSM: 8,
      paddingMD: 21,
      paddingLG: 34,
      paddingXL: 55,
      
      margin: 13,
      marginXS: 4,
      marginSM: 8,
      marginMD: 21,
      marginLG: 34,
      marginXL: 55,
      
      // Border radius
      borderRadius: 6,
      borderRadiusLG: 12,
      borderRadiusSM: 4,
      
      // Shadows
      boxShadow: '0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)',
      boxShadowSecondary: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
      
      // Motion
      motionDurationFast: '0.15s',
      motionDurationMid: '0.25s',
      motionDurationSlow: '0.4s',
      motionEaseInOut: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
      
      // Layout
      controlHeight: 34, // Golden ratio based
      controlHeightSM: 21,
      controlHeightLG: 55,
    },
    components: {
      Card: {
        borderRadius: 12,
        boxShadow: '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)',
        headerBg: isDarkMode ? '#262626' : '#fafafa',
      },
      Button: {
        borderRadius: 8,
        controlHeight: 34,
        fontWeight: 500,
      },
      Input: {
        borderRadius: 6,
        controlHeight: 34,
      },
      Select: {
        borderRadius: 6,
        controlHeight: 34,
      },
      Table: {
        borderRadius: 8,
        headerBg: isDarkMode ? '#262626' : '#fafafa',
      },
      Menu: {
        borderRadius: 8,
        itemBorderRadius: 6,
      },
      Modal: {
        borderRadius: 12,
      },
      Drawer: {
        borderRadius: 12,
      },
      Tag: {
        borderRadius: 4,
      },
      Badge: {
        borderRadius: 4,
      },
    },
  };

  const themeValue = {
    isDarkMode,
    toggleTheme,
    theme: isDarkMode ? 'dark' : 'light',
  };

  return (
    <ThemeContext.Provider value={themeValue}>
      <ConfigProvider theme={antdTheme}>
        {children}
      </ConfigProvider>
    </ThemeContext.Provider>
  );
};

export default CosmicThemeProvider;
