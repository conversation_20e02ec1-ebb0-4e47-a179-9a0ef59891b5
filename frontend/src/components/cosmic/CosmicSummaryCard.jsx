import React from 'react';
import { Tag, Divider, Row, Col, Spin, Tooltip, Typography } from 'antd';
import { 
  TrendingUpOutlined, 
  TrendingDownOutlined, 
  MinusOutlined,
  InfoCircleOutlined 
} from '@ant-design/icons';
import { useMoney } from '@/settings';
import { selectMoneyFormat } from '@/redux/settings/selectors';
import { useSelector } from 'react-redux';
import CosmicCard from './CosmicCard';
import './CosmicSummaryCard.css';

const { Text } = Typography;

export default function CosmicSummaryCard({ 
  title, 
  data, 
  prefix, 
  isLoading = false,
  trend = null, // 'up', 'down', 'neutral'
  trendValue = null,
  icon = null,
  status = 'primary', // 'primary', 'success', 'warning', 'error'
  subtitle = null,
  actionButton = null,
  ...props 
}) {
  const { moneyFormatter } = useMoney();
  const money_format_settings = useSelector(selectMoneyFormat);

  const getTrendIcon = () => {
    switch (trend) {
      case 'up':
        return <TrendingUpOutlined className="trend-icon trend-up" />;
      case 'down':
        return <TrendingDownOutlined className="trend-icon trend-down" />;
      case 'neutral':
        return <MinusOutlined className="trend-icon trend-neutral" />;
      default:
        return null;
    }
  };

  const getStatusColor = () => {
    switch (status) {
      case 'success':
        return 'var(--hvac-success)';
      case 'warning':
        return 'var(--hvac-warning)';
      case 'error':
        return 'var(--hvac-error)';
      default:
        return 'var(--hvac-primary)';
    }
  };

  const formatValue = (value) => {
    if (typeof value === 'number') {
      return moneyFormatter({
        amount: value,
        currency_code: money_format_settings?.default_currency_code,
      });
    }
    return value || '0';
  };

  return (
    <Col
      className="gutter-row"
      xs={{ span: 24 }}
      sm={{ span: 12 }}
      md={{ span: 12 }}
      lg={{ span: 6 }}
      {...props}
    >
      <CosmicCard 
        className={`cosmic-summary-card cosmic-summary-${status}`}
        hoverable={true}
        gradient={false}
      >
        <div className="cosmic-summary-content">
          {/* Header with icon and title */}
          <div className="cosmic-summary-header">
            <div className="cosmic-summary-title-section">
              {icon && (
                <div className="cosmic-summary-icon" style={{ color: getStatusColor() }}>
                  {icon}
                </div>
              )}
              <div className="cosmic-summary-title-text">
                <Text className="cosmic-summary-title" strong>
                  {title}
                </Text>
                {subtitle && (
                  <Text className="cosmic-summary-subtitle" type="secondary">
                    {subtitle}
                  </Text>
                )}
              </div>
            </div>
            {actionButton && (
              <div className="cosmic-summary-action">
                {actionButton}
              </div>
            )}
          </div>

          {/* Main value section */}
          <div className="cosmic-summary-value-section">
            <div className="cosmic-summary-prefix">
              <Text type="secondary" className="text-sm">
                {prefix}
              </Text>
            </div>
            
            <div className="cosmic-summary-value">
              {isLoading ? (
                <div className="cosmic-summary-loading">
                  <Spin size="large" />
                </div>
              ) : (
                <div className="cosmic-summary-main-value">
                  <Text className="cosmic-summary-number" style={{ color: getStatusColor() }}>
                    {formatValue(data)}
                  </Text>
                  {trend && trendValue && (
                    <div className="cosmic-summary-trend">
                      {getTrendIcon()}
                      <Text className={`trend-value trend-${trend}`}>
                        {trendValue}
                      </Text>
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>

          {/* Progress indicator */}
          <div className="cosmic-summary-progress">
            <div 
              className="cosmic-summary-progress-bar"
              style={{ backgroundColor: getStatusColor() }}
            />
          </div>
        </div>
      </CosmicCard>
    </Col>
  );
}
