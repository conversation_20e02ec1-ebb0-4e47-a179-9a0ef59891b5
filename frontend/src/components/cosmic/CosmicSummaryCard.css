/* COSMIC SUMMARY CARD STYLES */

.cosmic-summary-card {
  min-height: 140px;
  position: relative;
  overflow: hidden;
}

.cosmic-summary-content {
  display: flex;
  flex-direction: column;
  height: 100%;
  gap: var(--space-sm);
}

.cosmic-summary-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: var(--space-xs);
}

.cosmic-summary-title-section {
  display: flex;
  align-items: center;
  gap: var(--space-sm);
  flex: 1;
}

.cosmic-summary-icon {
  font-size: var(--font-size-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  background: rgba(24, 144, 255, 0.1);
  transition: var(--transition-base);
}

.cosmic-summary-title-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.cosmic-summary-title {
  font-size: var(--font-size-base);
  font-weight: 600;
  color: var(--hvac-gray-800);
  line-height: 1.2;
}

.cosmic-summary-subtitle {
  font-size: var(--font-size-xs);
  line-height: 1.2;
}

.cosmic-summary-action {
  opacity: 0;
  transition: var(--transition-base);
}

.cosmic-summary-card:hover .cosmic-summary-action {
  opacity: 1;
}

.cosmic-summary-value-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  gap: var(--space-xs);
}

.cosmic-summary-prefix {
  margin-bottom: var(--space-xs);
}

.cosmic-summary-value {
  display: flex;
  align-items: center;
  justify-content: center;
}

.cosmic-summary-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 40px;
}

.cosmic-summary-main-value {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-xs);
  width: 100%;
}

.cosmic-summary-number {
  font-size: var(--font-size-xl);
  font-weight: 700;
  line-height: 1;
  text-align: center;
  transition: var(--transition-base);
}

.cosmic-summary-trend {
  display: flex;
  align-items: center;
  gap: var(--space-xs);
  font-size: var(--font-size-sm);
}

.trend-icon {
  font-size: var(--font-size-sm);
}

.trend-up {
  color: var(--hvac-success);
}

.trend-down {
  color: var(--hvac-error);
}

.trend-neutral {
  color: var(--hvac-gray-500);
}

.trend-value {
  font-weight: 500;
}

.cosmic-summary-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--hvac-gray-200);
  overflow: hidden;
}

.cosmic-summary-progress-bar {
  height: 100%;
  width: 100%;
  background: var(--hvac-primary);
  transform: translateX(-100%);
  transition: transform 0.8s cubic-bezier(0.4, 0.0, 0.2, 1);
}

.cosmic-summary-card:hover .cosmic-summary-progress-bar {
  transform: translateX(0);
}

/* Status variants */
.cosmic-summary-success .cosmic-summary-icon {
  background: rgba(82, 196, 26, 0.1);
}

.cosmic-summary-warning .cosmic-summary-icon {
  background: rgba(250, 173, 20, 0.1);
}

.cosmic-summary-error .cosmic-summary-icon {
  background: rgba(255, 77, 79, 0.1);
}

/* Dark theme support */
[data-theme="dark"] .cosmic-summary-title {
  color: var(--hvac-gray-700);
}

[data-theme="dark"] .cosmic-summary-progress {
  background: var(--hvac-gray-300);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .cosmic-summary-card {
    min-height: 120px;
  }
  
  .cosmic-summary-number {
    font-size: var(--font-size-lg);
  }
  
  .cosmic-summary-title {
    font-size: var(--font-size-sm);
  }
  
  .cosmic-summary-icon {
    width: 28px;
    height: 28px;
    font-size: var(--font-size-md);
  }
}

@media (max-width: 480px) {
  .cosmic-summary-header {
    flex-direction: column;
    gap: var(--space-xs);
  }
  
  .cosmic-summary-action {
    opacity: 1;
    align-self: flex-end;
  }
}
