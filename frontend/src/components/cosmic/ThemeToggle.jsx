import React from 'react';
import { Button, Tooltip } from 'antd';
import { SunOutlined, MoonOutlined } from '@ant-design/icons';
import { useTheme } from './ThemeProvider';
import './ThemeToggle.css';

const ThemeToggle = ({ size = 'middle', className = '', ...props }) => {
  const { isDarkMode, toggleTheme } = useTheme();

  return (
    <Tooltip title={isDarkMode ? 'Przełącz na jasny motyw' : 'Przełącz na ciemny motyw'}>
      <Button
        type="text"
        size={size}
        className={`theme-toggle ${className}`}
        onClick={toggleTheme}
        icon={
          <div className="theme-toggle-icon">
            {isDarkMode ? (
              <SunOutlined className="sun-icon" />
            ) : (
              <MoonOutlined className="moon-icon" />
            )}
          </div>
        }
        {...props}
      />
    </Tooltip>
  );
};

export default ThemeToggle;
