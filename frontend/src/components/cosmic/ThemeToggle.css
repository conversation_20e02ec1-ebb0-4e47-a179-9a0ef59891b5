/* THEME TOGGLE BUTTON STYLES */

.theme-toggle {
  border-radius: var(--radius-md) !important;
  transition: var(--transition-base) !important;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.theme-toggle:hover {
  background: var(--hvac-gray-100) !important;
  transform: scale(1.05);
}

.theme-toggle-icon {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  transition: var(--transition-base);
}

.sun-icon,
.moon-icon {
  font-size: 16px;
  transition: var(--transition-base);
  position: absolute;
}

.sun-icon {
  color: var(--hvac-warning);
  animation: sun-rotate 20s linear infinite;
}

.moon-icon {
  color: var(--hvac-primary);
  animation: moon-glow 2s ease-in-out infinite alternate;
}

/* Sun rotation animation */
@keyframes sun-rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Moon glow animation */
@keyframes moon-glow {
  from {
    filter: drop-shadow(0 0 2px var(--hvac-primary));
  }
  to {
    filter: drop-shadow(0 0 8px var(--hvac-primary));
  }
}

/* Theme transition animation */
.theme-toggle-icon {
  animation: theme-switch 0.3s ease-in-out;
}

@keyframes theme-switch {
  0% {
    transform: scale(1) rotate(0deg);
  }
  50% {
    transform: scale(0.8) rotate(180deg);
  }
  100% {
    transform: scale(1) rotate(360deg);
  }
}

/* Dark theme adjustments */
[data-theme="dark"] .theme-toggle:hover {
  background: var(--hvac-gray-200) !important;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .theme-toggle {
    width: 36px;
    height: 36px;
  }
  
  .theme-toggle-icon {
    width: 18px;
    height: 18px;
  }
  
  .sun-icon,
  .moon-icon {
    font-size: 14px;
  }
}
