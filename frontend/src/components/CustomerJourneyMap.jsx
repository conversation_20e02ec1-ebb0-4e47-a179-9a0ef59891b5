/**
 * 🗺️ CUSTOMER JOURNEY MAPPING - AI-POWERED VISUALIZATION
 * 
 * <PERSON><PERSON><PERSON><PERSON><PERSON> mapowanie ścieżki klienta w Europie z AI insights:
 * - Visual journey timeline
 * - Emotion tracking
 * - Touchpoint analysis
 * - Predictive next steps
 * - Business impact scoring
 * - Cosmic-level visualization
 */

import React, { useState, useEffect, useCallback } from 'react';
import {
  Card,
  Row,
  Col,
  Timeline,
  Tag,
  Progress,
  Avatar,
  Space,
  Typography,
  Button,
  Tooltip,
  Badge,
  Rate,
  Statistic,
  Alert,
  Divider,
  Select,
  DatePicker
} from 'antd';
import {
  UserOutlined,
  HeartOutlined,
  PhoneOutlined,
  MailOutlined,
  HomeOutlined,
  ToolOutlined,
  DollarOutlined,
  TrendingUpOutlined,
  BulbOutlined,
  StarOutlined,
  ThunderboltOutlined,
  EyeOutlined,
  ClockCircleOutlined,
  EnvironmentOutlined,
  MessageOutlined
} from '@ant-design/icons';
import moment from 'moment';
import { CosmicCard, CosmicSummaryCard } from './cosmic';

const { Title, Text, Paragraph } = Typography;
const { Option } = Select;

const CustomerJourneyMap = ({ customerId, customer360Data }) => {
  const [selectedTimeframe, setSelectedTimeframe] = useState('6months');
  const [journeyData, setJourneyData] = useState(null);
  const [emotionAnalysis, setEmotionAnalysis] = useState(null);
  const [predictiveInsights, setPredictiveInsights] = useState(null);
  const [loading, setLoading] = useState(false);

  /**
   * 🎯 Generate Customer Journey Data
   */
  const generateJourneyData = useCallback(() => {
    if (!customer360Data) return;

    const journeyStages = [
      {
        stage: 'awareness',
        title: '🔍 Świadomość',
        description: 'Klient dowiaduje się o naszych usługach',
        emotion: 'curious',
        emotionScore: 0.6,
        touchpoints: ['Google Search', 'Rekomendacja'],
        date: moment().subtract(8, 'months'),
        businessImpact: 'low',
        nextProbableActions: ['Zapytanie o ofertę', 'Wizyta na stronie']
      },
      {
        stage: 'consideration',
        title: '🤔 Rozważanie',
        description: 'Porównywanie ofert i dostawców',
        emotion: 'analytical',
        emotionScore: 0.7,
        touchpoints: ['Telefon', 'Email', 'Wizyta techniczna'],
        date: moment().subtract(7, 'months'),
        businessImpact: 'medium',
        nextProbableActions: ['Prośba o wycenę', 'Wizyta w showroom']
      },
      {
        stage: 'purchase',
        title: '💰 Zakup',
        description: 'Decyzja o zakupie i podpisanie umowy',
        emotion: 'excited',
        emotionScore: 0.9,
        touchpoints: ['Spotkanie osobiste', 'Podpisanie umowy'],
        date: moment().subtract(6, 'months'),
        businessImpact: 'high',
        value: 25000,
        nextProbableActions: ['Instalacja', 'Przygotowanie lokalizacji']
      },
      {
        stage: 'onboarding',
        title: '🏗️ Wdrożenie',
        description: 'Instalacja i uruchomienie systemu',
        emotion: 'satisfied',
        emotionScore: 0.8,
        touchpoints: ['Instalacja', 'Szkolenie', 'Testy'],
        date: moment().subtract(5, 'months'),
        businessImpact: 'high',
        nextProbableActions: ['Pierwszy serwis', 'Feedback']
      },
      {
        stage: 'usage',
        title: '🔧 Użytkowanie',
        description: 'Regularne korzystanie z systemu',
        emotion: 'content',
        emotionScore: 0.75,
        touchpoints: ['Serwis', 'Wsparcie techniczne'],
        date: moment().subtract(3, 'months'),
        businessImpact: 'medium',
        nextProbableActions: ['Serwis okresowy', 'Rozbudowa systemu']
      },
      {
        stage: 'advocacy',
        title: '⭐ Rekomendacja',
        description: 'Polecanie naszych usług innym',
        emotion: 'delighted',
        emotionScore: 0.95,
        touchpoints: ['Referencje', 'Opinie online'],
        date: moment().subtract(1, 'month'),
        businessImpact: 'very_high',
        nextProbableActions: ['Kolejny projekt', 'Rekomendacje']
      }
    ];

    // Add real interactions from customer360Data
    if (customer360Data?.interactionHistory?.recentInteractions) {
      const realInteractions = customer360Data.interactionHistory.recentInteractions.map(interaction => ({
        stage: 'interaction',
        title: `📞 ${interaction.type.toUpperCase()}`,
        description: interaction.summary,
        emotion: interaction.sentiment,
        emotionScore: getEmotionScore(interaction.sentiment),
        touchpoints: [interaction.type],
        date: moment(interaction.date),
        businessImpact: interaction.priority === 'high' ? 'high' : 'medium',
        realInteraction: true
      }));

      journeyStages.push(...realInteractions);
    }

    // Sort by date
    journeyStages.sort((a, b) => moment(a.date).diff(moment(b.date)));

    setJourneyData(journeyStages);
  }, [customer360Data]);

  /**
   * 🎭 Get Emotion Score
   */
  const getEmotionScore = useCallback((emotion) => {
    const scores = {
      very_positive: 0.95,
      positive: 0.8,
      neutral: 0.6,
      negative: 0.3,
      very_negative: 0.1,
      delighted: 0.95,
      satisfied: 0.8,
      content: 0.75,
      excited: 0.9,
      analytical: 0.7,
      curious: 0.6
    };
    return scores[emotion] || 0.6;
  }, []);

  /**
   * 🎨 Get Emotion Color
   */
  const getEmotionColor = useCallback((emotion) => {
    const colors = {
      very_positive: '#52c41a',
      positive: '#73d13d',
      neutral: '#faad14',
      negative: '#ff7875',
      very_negative: '#ff4d4f',
      delighted: '#52c41a',
      satisfied: '#73d13d',
      content: '#95de64',
      excited: '#1890ff',
      analytical: '#722ed1',
      curious: '#faad14'
    };
    return colors[emotion] || '#d9d9d9';
  }, []);

  /**
   * 🎯 Get Business Impact Icon
   */
  const getBusinessImpactIcon = useCallback((impact) => {
    const icons = {
      low: <EyeOutlined style={{ color: 'var(--hvac-gray-500)' }} />,
      medium: <TrendingUpOutlined style={{ color: 'var(--hvac-warning)' }} />,
      high: <DollarOutlined style={{ color: 'var(--hvac-success)' }} />,
      very_high: <StarOutlined style={{ color: 'var(--hvac-primary)' }} />
    };
    return icons[impact] || icons.medium;
  }, []);

  /**
   * 📊 Generate Emotion Analysis
   */
  const generateEmotionAnalysis = useCallback(() => {
    if (!journeyData) return;

    const emotions = journeyData.map(stage => ({
      stage: stage.stage,
      emotion: stage.emotion,
      score: stage.emotionScore,
      date: stage.date
    }));

    const averageEmotion = emotions.reduce((sum, e) => sum + e.score, 0) / emotions.length;
    const emotionTrend = emotions.slice(-3).map(e => e.score);
    const isImproving = emotionTrend[emotionTrend.length - 1] > emotionTrend[0];

    setEmotionAnalysis({
      averageScore: averageEmotion,
      trend: isImproving ? 'improving' : 'declining',
      emotions: emotions,
      riskLevel: averageEmotion < 0.5 ? 'high' : averageEmotion < 0.7 ? 'medium' : 'low'
    });
  }, [journeyData]);

  /**
   * 🔮 Generate Predictive Insights
   */
  const generatePredictiveInsights = useCallback(() => {
    if (!journeyData || !emotionAnalysis) return;

    const insights = {
      nextBestActions: [
        {
          action: 'Zaproponuj rozbudowę systemu',
          probability: 0.75,
          timing: 'W ciągu 2 miesięcy',
          expectedValue: 15000,
          reasoning: 'Wysoka satysfakcja i historia pozytywnych interakcji'
        },
        {
          action: 'Umów serwis profilaktyczny',
          probability: 0.9,
          timing: 'W ciągu 3 tygodni',
          expectedValue: 500,
          reasoning: 'Zbliża się termin okresowego przeglądu'
        }
      ],
      churnRisk: emotionAnalysis.averageScore < 0.6 ? 'high' : 'low',
      upsellProbability: emotionAnalysis.averageScore * 0.8,
      recommendationLikelihood: emotionAnalysis.averageScore > 0.8 ? 'very_high' : 'medium'
    };

    setPredictiveInsights(insights);
  }, [journeyData, emotionAnalysis]);

  // Initialize data
  useEffect(() => {
    generateJourneyData();
  }, [generateJourneyData]);

  useEffect(() => {
    generateEmotionAnalysis();
  }, [generateEmotionAnalysis]);

  useEffect(() => {
    generatePredictiveInsights();
  }, [generatePredictiveInsights]);

  if (!journeyData) {
    return (
      <CosmicCard>
        <div style={{ textAlign: 'center', padding: 'var(--space-xl)' }}>
          <Text>Loading customer journey...</Text>
        </div>
      </CosmicCard>
    );
  }

  return (
    <div className="animate-fade-in">
      {/* Header */}
      <div style={{ marginBottom: 'var(--space-lg)' }}>
        <Title level={3} style={{ color: 'var(--hvac-primary)' }}>
          🗺️ Customer Journey Map
        </Title>
        <Text type="secondary">
          AI-powered visualization of customer touchpoints and emotions
        </Text>
      </div>

      {/* Emotion Analysis Summary */}
      {emotionAnalysis && (
        <Row gutter={[16, 16]} style={{ marginBottom: 'var(--space-lg)' }}>
          <Col xs={24} sm={6}>
            <CosmicSummaryCard hoverable>
              <Statistic
                title="💖 Average Emotion"
                value={Math.round(emotionAnalysis.averageScore * 100)}
                suffix="%"
                prefix={<HeartOutlined style={{ color: 'var(--hvac-success)' }} />}
                valueStyle={{ color: 'var(--hvac-success)' }}
              />
            </CosmicSummaryCard>
          </Col>
          <Col xs={24} sm={6}>
            <CosmicSummaryCard hoverable>
              <Statistic
                title="📈 Emotion Trend"
                value={emotionAnalysis.trend}
                prefix={<TrendingUpOutlined style={{ 
                  color: emotionAnalysis.trend === 'improving' ? 'var(--hvac-success)' : 'var(--hvac-warning)' 
                }} />}
                valueStyle={{ 
                  color: emotionAnalysis.trend === 'improving' ? 'var(--hvac-success)' : 'var(--hvac-warning)' 
                }}
              />
            </CosmicSummaryCard>
          </Col>
          <Col xs={24} sm={6}>
            <CosmicSummaryCard hoverable>
              <Statistic
                title="⚠️ Risk Level"
                value={emotionAnalysis.riskLevel}
                prefix={<ThunderboltOutlined style={{ 
                  color: emotionAnalysis.riskLevel === 'high' ? 'var(--hvac-error)' : 'var(--hvac-success)' 
                }} />}
                valueStyle={{ 
                  color: emotionAnalysis.riskLevel === 'high' ? 'var(--hvac-error)' : 'var(--hvac-success)' 
                }}
              />
            </CosmicSummaryCard>
          </Col>
          <Col xs={24} sm={6}>
            <CosmicSummaryCard hoverable>
              <Statistic
                title="🎯 Journey Stages"
                value={journeyData.length}
                prefix={<EnvironmentOutlined style={{ color: 'var(--hvac-primary)' }} />}
                valueStyle={{ color: 'var(--hvac-primary)' }}
              />
            </CosmicSummaryCard>
          </Col>
        </Row>
      )}

      {/* Journey Timeline */}
      <Row gutter={[16, 16]}>
        <Col xs={24} lg={16}>
          <CosmicCard
            title={
              <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                <EnvironmentOutlined style={{ color: 'var(--hvac-primary)' }} />
                🗺️ Customer Journey Timeline
              </div>
            }
            hoverable
          >
            <Timeline mode="left">
              {journeyData.map((stage, index) => (
                <Timeline.Item
                  key={index}
                  color={getEmotionColor(stage.emotion)}
                  dot={
                    <Avatar
                      size="small"
                      style={{
                        backgroundColor: getEmotionColor(stage.emotion),
                        border: '2px solid white'
                      }}
                      icon={getBusinessImpactIcon(stage.businessImpact)}
                    />
                  }
                  className="animate-slide-in"
                  style={{ animationDelay: `${index * 0.1}s` }}
                >
                  <CosmicCard
                    size="small"
                    hoverable
                    style={{
                      marginBottom: 'var(--space-sm)',
                      borderLeft: `4px solid ${getEmotionColor(stage.emotion)}`
                    }}
                  >
                    <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                      <div style={{ flex: 1 }}>
                        <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)', marginBottom: 'var(--space-xs)' }}>
                          <Text strong style={{ color: 'var(--hvac-primary)', fontSize: 'var(--font-size-md)' }}>
                            {stage.title}
                          </Text>
                          {stage.realInteraction && (
                            <Tag color="blue" size="small">Real Data</Tag>
                          )}
                        </div>

                        <div style={{
                          color: 'var(--hvac-gray-600)',
                          fontSize: 'var(--font-size-sm)',
                          marginBottom: 'var(--space-sm)'
                        }}>
                          📅 {moment(stage.date).format('DD/MM/YYYY')} ({moment(stage.date).fromNow()})
                        </div>

                        <Paragraph style={{ marginBottom: 'var(--space-sm)' }}>
                          {stage.description}
                        </Paragraph>

                        <div style={{ marginBottom: 'var(--space-sm)' }}>
                          <Text strong style={{ color: 'var(--hvac-gray-700)' }}>💖 Emotion: </Text>
                          <Rate
                            disabled
                            value={stage.emotionScore * 5}
                            style={{ fontSize: '12px' }}
                          />
                          <Text style={{ marginLeft: 'var(--space-xs)', color: getEmotionColor(stage.emotion) }}>
                            {stage.emotion} ({Math.round(stage.emotionScore * 100)}%)
                          </Text>
                        </div>

                        <div style={{ marginBottom: 'var(--space-sm)' }}>
                          <Text strong style={{ color: 'var(--hvac-gray-700)' }}>📍 Touchpoints: </Text>
                          <Space wrap>
                            {stage.touchpoints.map((touchpoint, i) => (
                              <Tag key={i} color="blue" size="small">
                                {touchpoint}
                              </Tag>
                            ))}
                          </Space>
                        </div>

                        {stage.value && (
                          <div style={{ marginBottom: 'var(--space-sm)' }}>
                            <Text strong style={{ color: 'var(--hvac-success)' }}>
                              💰 Value: {new Intl.NumberFormat('pl-PL', {
                                style: 'currency',
                                currency: 'PLN'
                              }).format(stage.value)}
                            </Text>
                          </div>
                        )}

                        {stage.nextProbableActions && (
                          <div>
                            <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🎯 Next Actions: </Text>
                            <div style={{ marginTop: 'var(--space-xs)' }}>
                              {stage.nextProbableActions.map((action, i) => (
                                <Tag key={i} color="orange" size="small" style={{ marginBottom: 'var(--space-xs)' }}>
                                  {action}
                                </Tag>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </CosmicCard>
                </Timeline.Item>
              ))}
            </Timeline>
          </CosmicCard>
        </Col>

        <Col xs={24} lg={8}>
          {/* Predictive Insights */}
          {predictiveInsights && (
            <CosmicCard
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                  <BulbOutlined style={{ color: 'var(--hvac-success)' }} />
                  🔮 Predictive Insights
                </div>
              }
              hoverable
              style={{ marginBottom: 'var(--space-md)' }}
            >
              <Space direction="vertical" style={{ width: '100%' }} size="middle">
                <div>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🎯 Next Best Actions:</Text>
                  <div style={{ marginTop: 'var(--space-sm)' }}>
                    {predictiveInsights.nextBestActions.map((action, index) => (
                      <div
                        key={index}
                        style={{
                          padding: 'var(--space-sm)',
                          background: 'var(--hvac-gray-50)',
                          borderRadius: 'var(--radius-base)',
                          marginBottom: 'var(--space-xs)'
                        }}
                      >
                        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 'var(--space-xs)' }}>
                          <Text strong style={{ fontSize: 'var(--font-size-sm)' }}>
                            {action.action}
                          </Text>
                          <Tag color="green">
                            {Math.round(action.probability * 100)}%
                          </Tag>
                        </div>
                        <Text type="secondary" style={{ fontSize: 'var(--font-size-xs)' }}>
                          ⏰ {action.timing}
                        </Text>
                        <div>
                          <Text strong style={{ color: 'var(--hvac-success)', fontSize: 'var(--font-size-xs)' }}>
                            💰 {new Intl.NumberFormat('pl-PL', {
                              style: 'currency',
                              currency: 'PLN'
                            }).format(action.expectedValue)}
                          </Text>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                <Divider style={{ margin: 'var(--space-sm) 0' }} />

                <div>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>📊 Risk Assessment:</Text>
                  <div style={{ marginTop: 'var(--space-sm)' }}>
                    <Progress
                      percent={predictiveInsights.churnRisk === 'high' ? 80 : 20}
                      strokeColor={predictiveInsights.churnRisk === 'high' ? 'var(--hvac-error)' : 'var(--hvac-success)'}
                      format={() => predictiveInsights.churnRisk}
                    />
                  </div>
                </div>

                <div>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>🚀 Upsell Probability:</Text>
                  <div style={{ marginTop: 'var(--space-sm)' }}>
                    <Progress
                      percent={Math.round(predictiveInsights.upsellProbability * 100)}
                      strokeColor="var(--hvac-primary)"
                    />
                  </div>
                </div>

                <div>
                  <Text strong style={{ color: 'var(--hvac-gray-700)' }}>⭐ Recommendation Likelihood:</Text>
                  <div style={{ marginTop: 'var(--space-sm)' }}>
                    <Tag
                      color={predictiveInsights.recommendationLikelihood === 'very_high' ? 'green' : 'orange'}
                      style={{ fontSize: 'var(--font-size-sm)' }}
                    >
                      {predictiveInsights.recommendationLikelihood}
                    </Tag>
                  </div>
                </div>
              </Space>
            </CosmicCard>
          )}

          {/* Emotion Trend Chart */}
          {emotionAnalysis && (
            <CosmicCard
              title={
                <div style={{ display: 'flex', alignItems: 'center', gap: 'var(--space-sm)' }}>
                  <HeartOutlined style={{ color: 'var(--hvac-error)' }} />
                  💖 Emotion Trend
                </div>
              }
              hoverable
            >
              <div style={{ textAlign: 'center', padding: 'var(--space-md)' }}>
                <Progress
                  type="circle"
                  percent={Math.round(emotionAnalysis.averageScore * 100)}
                  strokeColor={{
                    '0%': '#ff4d4f',
                    '50%': '#faad14',
                    '100%': '#52c41a',
                  }}
                  format={(percent) => (
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: 'var(--font-size-lg)', fontWeight: 'bold' }}>
                        {percent}%
                      </div>
                      <div style={{ fontSize: 'var(--font-size-xs)', color: 'var(--hvac-gray-500)' }}>
                        Satisfaction
                      </div>
                    </div>
                  )}
                />
                <div style={{ marginTop: 'var(--space-md)' }}>
                  <Text type="secondary">
                    Customer emotion trend over time
                  </Text>
                </div>
              </div>
            </CosmicCard>
          )}
        </Col>
      </Row>
    </div>
  );
};

export default CustomerJourneyMap;
